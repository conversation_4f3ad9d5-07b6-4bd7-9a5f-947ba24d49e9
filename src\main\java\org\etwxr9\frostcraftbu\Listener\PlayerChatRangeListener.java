package org.etwxr9.frostcraftbu.Listener;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Item.ItemManager;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;

public class PlayerChatRangeListener implements Listener {

    @EventHandler
    public void onChat(AsyncPlayerChatEvent e) {
        if (e.getPlayer().isOp()) {
            return;
        }
        boolean radio = false;
        var towerObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.towerObj");
        var currentHeight = Bukkit.getScoreboardManager().getMainScoreboard().getObjective(towerObj)
                .getScore(e.getPlayer().getName()).getScore();
        if (e.getPlayer().getInventory().getItemInMainHand()
                .isSimilar(ItemManager.getItem(FrostCraftBU.i().getConfig().getString("InterphoneId")))) {
            if (currentHeight > FrostCraftBU.i().getConfig().getInt("ScoreboardObjectives.towerHeight")) {
                radio = true;
            } else {
                e.getPlayer().sendMessage(
                        Component.text("灯塔未达到指定高度（" + currentHeight + "）无法发出无线电信息！").color(NamedTextColor.RED));
            }
        }

        // 从记分板获取该玩家聊天距离
        var nearObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.PlayerChatRangeNearObj");
        var mediumObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.PlayerChatRangeMediumObj");
        var farObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.PlayerChatRangeFarObj");
        int chatRangeNear = Bukkit.getScoreboardManager().getMainScoreboard().getObjective(nearObj)
                .getScore(e.getPlayer().getName()).getScore();
        int chatRangeMedium = Bukkit.getScoreboardManager().getMainScoreboard()
                .getObjective(mediumObj)
                .getScore(e.getPlayer().getName()).getScore();
        int chatRangeFar = Bukkit.getScoreboardManager().getMainScoreboard().getObjective(farObj)
                .getScore(e.getPlayer().getName()).getScore();
        // 获取该玩家距离其他玩家的距离
        for (Player p : Bukkit.getOnlinePlayers()) {
            if (p == e.getPlayer()) {
                continue;
            }
            Location playerLoc = e.getPlayer().getLocation();
            Location pLoc = p.getLocation();
            double distance = playerLoc.distance(pLoc);
            if (distance <= chatRangeNear) {
                p.sendMessage(
                        Component.text(e.getPlayer().getName() + ": " + e.getMessage()).color(NamedTextColor.WHITE));
            } else if (distance <= chatRangeMedium) {
                p.sendMessage(
                        Component.text(e.getPlayer().getName() + ": " + e.getMessage()).color(NamedTextColor.GRAY));
            } else if (distance <= chatRangeFar) {
                p.sendMessage(Component.text(e.getPlayer().getName() + ": " + e.getMessage())
                        .color(NamedTextColor.DARK_GRAY));
            }
            // 如果对方背包有对讲机，接收该信息
            else if (radio &&
                    p.getInventory()
                            .contains(ItemManager.getItem(FrostCraftBU.i().getConfig().getString("InterphoneId")))) {
                p.sendMessage(Component.text("（无线电）" + e.getPlayer().getName() + ": " + e.getMessage())
                        .color(NamedTextColor.WHITE).decorate(TextDecoration.ITALIC));
            }
        }
        e.setCancelled(true);
    }

}
