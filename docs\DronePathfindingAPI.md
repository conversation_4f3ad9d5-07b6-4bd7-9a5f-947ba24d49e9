# 无人机寻路API文档

## 概述

DronePathfindingService 是 FrostCraftBU 插件提供的无人机寻路核心服务，基于 pathetic-bukkit 库实现，为整个插件提供统一的3D寻路功能。

## 主要特性

- **异步寻路**: 使用 CompletableFuture 实现非阻塞寻路
- **路径平滑**: 支持多种平滑算法（线性插值、Catmull-Rom样条、贝塞尔曲线）
- **3D寻路**: 专为无人机设计的3D空间寻路算法
- **高度偏好**: 智能的高度成本计算，偏好在地面上方2格高度飞行
- **备用策略**: 当主要寻路失败时自动使用备用策略
- **性能优化**: 支持区块加载和高效的路径计算

## 快速开始

### 获取服务实例

```java
DronePathfindingService service = FrostCraftBU.getDronePathfindingService();
if (service == null) {
    // 服务未初始化，处理错误
    return;
}
```

### 基本异步寻路

```java
service.findPathAsync(startLocation, endLocation)
    .thenAccept(result -> {
        if (result.isSuccessful()) {
            List<Location> path = result.getRawPath();
            // 处理路径
        } else {
            // 处理寻路失败
        }
    });
```

### 带平滑的高级寻路

```java
service.findPathAsync(startLocation, endLocation, 
                     true, // 启用平滑
                     PathSmoothingUtil.SmoothingType.CATMULL_ROM, 
                     15) // 分辨率
    .thenAccept(result -> {
        if (result.isSuccessful()) {
            List<Location> smoothPath = result.getSmoothedPath();
            // 使用平滑路径
        }
    });
```

## API 参考

### DronePathfindingService

#### 主要方法

##### findPathAsync(Location start, Location end)
- **描述**: 执行基本的异步寻路
- **参数**: 
  - `start`: 起点位置
  - `end`: 终点位置
- **返回**: `CompletableFuture<DronePathResult>`

##### findPathAsync(Location start, Location end, boolean enableSmoothing, SmoothingType smoothingType, int resolution)
- **描述**: 执行带平滑的高级异步寻路
- **参数**:
  - `start`: 起点位置
  - `end`: 终点位置
  - `enableSmoothing`: 是否启用路径平滑
  - `smoothingType`: 平滑算法类型
  - `resolution`: 平滑分辨率（1-20）
- **返回**: `CompletableFuture<DronePathResult>`

##### findPathSync(Location start, Location end)
- **描述**: 执行同步寻路（阻塞调用）
- **注意**: 应在异步上下文中使用，避免阻塞主线程
- **返回**: `DronePathResult`

### DronePathResult

#### 主要属性

- `getState()`: 获取寻路状态
- `getRawPath()`: 获取原始路径点列表
- `getSmoothedPath()`: 获取平滑路径点列表
- `getPathLength()`: 获取路径总长度
- `getComputeTime()`: 获取计算耗时
- `getErrorMessage()`: 获取错误信息

#### 主要方法

- `isSuccessful()`: 检查寻路是否成功
- `hasFallback()`: 检查是否使用了备用策略
- `getRecommendedPath()`: 获取推荐路径（优先返回平滑路径）
- `getPathSize()`: 获取路径点数量

### PathSmoothingUtil

#### 平滑算法类型

- `LINEAR`: 线性插值
- `CATMULL_ROM`: Catmull-Rom样条曲线（推荐）
- `BEZIER`: 贝塞尔曲线

#### 主要方法

- `smoothPath(List<Location> rawPath, SmoothingType type, int resolution)`: 对路径进行平滑处理
- `calculatePathLength(List<Location> path)`: 计算路径总长度

## 寻路状态

### PathState 枚举

- `SUCCESS`: 寻路成功
- `FALLBACK`: 使用备用策略成功
- `FAILED`: 寻路失败
- `NO_PATH`: 无法找到路径
- `INVALID_INPUT`: 输入参数无效

## 最佳实践

### 1. 异步处理

始终使用异步方法避免阻塞主线程：

```java
service.findPathAsync(start, end)
    .thenAccept(result -> {
        // 在主线程中更新UI
        Bukkit.getScheduler().runTask(plugin, () -> {
            player.sendMessage("寻路完成！");
        });
    });
```

### 2. 错误处理

总是检查寻路结果和处理异常：

```java
service.findPathAsync(start, end)
    .thenAccept(result -> {
        if (result.isSuccessful()) {
            // 处理成功结果
        } else {
            // 处理失败，显示错误信息
            player.sendMessage("寻路失败: " + result.getErrorMessage());
        }
    })
    .exceptionally(throwable -> {
        // 处理异常
        throwable.printStackTrace();
        return null;
    });
```

### 3. 路径平滑选择

- **线性插值**: 适用于简单路径，性能最好
- **Catmull-Rom样条**: 适用于大多数情况，平滑效果好
- **贝塞尔曲线**: 适用于需要特殊曲线效果的场景

### 4. 分辨率设置

- 低分辨率 (5-8): 性能优先，适用于长距离路径
- 中分辨率 (10-15): 平衡性能和效果，推荐设置
- 高分辨率 (16-20): 效果优先，适用于短距离精细路径

## 性能考虑

### 1. 寻路距离

- 短距离 (<100格): 性能良好
- 中距离 (100-500格): 可能需要几百毫秒
- 长距离 (>500格): 可能需要数秒，建议分段处理

### 2. 并发限制

避免同时执行过多寻路任务，建议：
- 单个玩家同时最多2-3个寻路任务
- 服务器总体控制在10-20个并发任务

### 3. 内存使用

- 原始路径通常占用较少内存
- 平滑路径会增加内存使用，特别是高分辨率设置
- 及时释放不需要的路径数据

## 故障排除

### 常见问题

1. **服务未初始化**: 确保主插件已正确加载
2. **寻路失败**: 检查起点和终点是否合理
3. **性能问题**: 降低分辨率或分段处理长路径
4. **内存泄漏**: 确保及时释放路径数据

### 调试信息

使用 `DronePathResult.toString()` 获取详细的寻路信息：

```java
System.out.println(result.toString());
// 输出: DronePathResult{state=SUCCESS, pathSize=45, pathLength=123.45, computeTime=234ms}
```

## 示例代码

完整的使用示例请参考 `DronePathfindingExample.java` 文件。
