SampleConfig: "Hello, World!"

SaveBuildingSign0: "§7[FC]保存建筑"
OccupiedLandSign0: "§7[FC]管理建筑"
OccupiedLandSign1: "§7右键打开菜单"

# 建设牌子
EmptyLandSign0: "§7[FC]空地建设"
EmptyLandSign1: "§7请选择建筑"
EmptyLandSign2List:
  小地皮: 小工地
  中地皮: 中工地
  大地皮: 大工地
unableToDestroyList:
  - 大地皮
ShowParticleInfo: "显示粒子信息"
SaveBuildingInfo: "建筑{name}已保存"

# 生产牌子
ProductionSign0: "[FC]配方选择" # 生产牌子的第一行标识
ProductionSign1: "§7右键打开菜单" # 生产牌子的第二行提示
GatherSign0: "[FC]资源采集" # 采集牌子的第一行标识
GatherSign1: "§7右键打开菜单" # 采集牌子的第二行提示
ConsumeSign0: "[FC]资源消耗" # 消耗牌子的第一行标识
ConsumeSign1: "§7右键打开菜单" # 消耗牌子的第二行提示

AlreadyBuilding: "§c该地块正在建设中！"
AlreadyHaveBuilding: "§c该地块已建设建筑！"

# 记分板目标名称
ScoreboardObjectives:
  WoodReq: "fc_wood_req"
  SteelReq: "fc_steel_req"
  WoodCurrent: "fc_wood_current"
  SteelCurrent: "fc_steel_current"
  # 科研记分项
  StatusObjName: "#ResearchStatus"
  # 木板和钢材的物品名称与 TechTree.yml 中的 consume 键一致
  WoodMaterialName: "石头" # 在 TechTree.yml 中使用的木板名称
  SteelMaterialName: "木头" # 在 TechTree.yml 中使用的钢材名称
  RealTimeObj: "fc_real_time_obj" # 实时记分项
  RealTimeEntry: "fc_real_time_entry" # 实时记分项
  SteamObj: "fc_steam" # 蒸汽记分项
  SteamEntry: "fc_steam_entry" # 蒸汽记分项
  SteamMax: 100
  ResearchConsumeSteam: 1
