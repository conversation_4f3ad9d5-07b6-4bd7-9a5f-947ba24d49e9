package org.etwxr9.frostcraftbu.pathfinding.api;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.etwxr9.frostcraftbu.FrostCraftBU;

import java.util.List;

/**
 * 实时无人机运动示例
 * 
 * 展示如何使用实时位置计算功能来实现平滑的无人机运动
 */
public class RealtimeDroneExample {
    
    /**
     * 实时无人机运动任务
     */
    public static class RealtimeDroneTask extends BukkitRunnable {
        
        private final List<Location> path;
        private final double speed; // 米/秒
        private final PathSmoothingUtil.SmoothingType smoothingType;
        private final Player player;
        private final DroneMovementCallback callback;
        
        private int elapsedTicks = 0;
        private boolean completed = false;
        
        public RealtimeDroneTask(List<Location> path, double speed, 
                               PathSmoothingUtil.SmoothingType smoothingType,
                               Player player, DroneMovementCallback callback) {
            this.path = path;
            this.speed = speed;
            this.smoothingType = smoothingType;
            this.player = player;
            this.callback = callback;
        }
        
        @Override
        public void run() {
            if (completed) {
                cancel();
                return;
            }
            
            // 检查路径是否完成
            if (PathSmoothingUtil.isPathCompleted(elapsedTicks, speed, path)) {
                completed = true;
                if (callback != null) {
                    callback.onPathCompleted();
                }
                cancel();
                return;
            }
            
            // 计算当前位置
            Location currentPosition = PathSmoothingUtil.calculateRealtimePosition(
                elapsedTicks, speed, path, smoothingType);
            
            if (currentPosition == null) {
                completed = true;
                cancel();
                return;
            }
            
            // 计算运动方向（用于旋转）
            PathSmoothingUtil.Vector3D direction = PathSmoothingUtil.calculateMovementDirection(
                elapsedTicks, speed, path, smoothingType);
            
            // 计算进度
            double progress = PathSmoothingUtil.calculatePathProgress(elapsedTicks, speed, path);
            
            // 回调处理位置更新
            if (callback != null) {
                callback.onPositionUpdate(currentPosition, direction, progress, elapsedTicks);
            }
            
            elapsedTicks++;
        }
        
        /**
         * 获取预计完成时间（tick）
         */
        public int getEstimatedDurationTicks() {
            return PathSmoothingUtil.calculatePathDurationTicks(speed, path);
        }
        
        /**
         * 获取当前进度
         */
        public double getCurrentProgress() {
            return PathSmoothingUtil.calculatePathProgress(elapsedTicks, speed, path);
        }
        
        /**
         * 强制停止
         */
        public void forceStop() {
            completed = true;
            if (callback != null) {
                callback.onPathStopped();
            }
            cancel();
        }
    }
    
    /**
     * 无人机运动回调接口
     */
    public interface DroneMovementCallback {
        /**
         * 位置更新回调
         * @param position 当前位置
         * @param direction 运动方向向量（已标准化）
         * @param progress 路径进度（0.0-1.0）
         * @param elapsedTicks 已运行tick数
         */
        void onPositionUpdate(Location position, PathSmoothingUtil.Vector3D direction, 
                            double progress, int elapsedTicks);
        
        /**
         * 路径完成回调
         */
        void onPathCompleted();
        
        /**
         * 路径停止回调
         */
        void onPathStopped();
    }
    
    /**
     * 创建并启动实时无人机任务
     * 
     * @param path 路径点列表
     * @param speedMetersPerSecond 速度（米/秒）
     * @param smoothingType 平滑算法类型
     * @param player 玩家（用于上下文）
     * @param callback 运动回调
     * @return 任务实例
     */
    public static RealtimeDroneTask startRealtimeDrone(List<Location> path, double speedMetersPerSecond,
                                                      PathSmoothingUtil.SmoothingType smoothingType,
                                                      Player player, DroneMovementCallback callback) {
        RealtimeDroneTask task = new RealtimeDroneTask(path, speedMetersPerSecond, smoothingType, player, callback);
        
        // 每tick运行一次
        task.runTaskTimer(FrostCraftBU.i(), 0L, 1L);
        
        return task;
    }
    
    /**
     * 示例：创建一个简单的粒子效果无人机
     */
    public static void createParticleDroneExample(Player player, List<Location> path) {
        double speed = 5.0; // 5米/秒
        PathSmoothingUtil.SmoothingType smoothing = PathSmoothingUtil.SmoothingType.CATMULL_ROM;
        
        DroneMovementCallback callback = new DroneMovementCallback() {
            @Override
            public void onPositionUpdate(Location position, PathSmoothingUtil.Vector3D direction, 
                                       double progress, int elapsedTicks) {
                // 每5tick显示一次粒子效果
                if (elapsedTicks % 5 == 0) {
                    // 这里可以生成粒子效果
                    // player.getWorld().spawnParticle(Particle.VILLAGER_HAPPY, position, 1);
                    
                    // 发送进度信息
                    if (elapsedTicks % 20 == 0) { // 每秒一次
                        player.sendMessage(String.format("§7无人机进度: §a%.1f%%", progress * 100));
                    }
                }
            }
            
            @Override
            public void onPathCompleted() {
                player.sendMessage("§a无人机已到达目标位置！");
            }
            
            @Override
            public void onPathStopped() {
                player.sendMessage("§c无人机运动已停止");
            }
        };
        
        RealtimeDroneTask task = startRealtimeDrone(path, speed, smoothing, player, callback);
        
        int estimatedTicks = task.getEstimatedDurationTicks();
        double estimatedSeconds = estimatedTicks / 20.0;
        
        player.sendMessage("§6无人机开始运动！");
        player.sendMessage("§7预计耗时: §f" + String.format("%.1f", estimatedSeconds) + " 秒");
        player.sendMessage("§7速度: §f" + speed + " 米/秒");
        player.sendMessage("§7平滑算法: §f" + smoothing.name());
    }
    
    /**
     * 示例：创建一个实体跟随路径的无人机
     */
    public static void createEntityDroneExample(Player player, List<Location> path, 
                                               org.bukkit.entity.Entity droneEntity) {
        double speed = 3.0; // 3米/秒
        PathSmoothingUtil.SmoothingType smoothing = PathSmoothingUtil.SmoothingType.CATMULL_ROM;
        
        DroneMovementCallback callback = new DroneMovementCallback() {
            @Override
            public void onPositionUpdate(Location position, PathSmoothingUtil.Vector3D direction, 
                                       double progress, int elapsedTicks) {
                // 移动实体到新位置
                droneEntity.teleport(position);
                
                // 可以根据方向向量设置实体的朝向
                if (direction != null && direction.length() > 0.01) {
                    float yaw = (float) Math.toDegrees(Math.atan2(-direction.x, direction.z));
                    float pitch = (float) Math.toDegrees(Math.asin(-direction.y));
                    
                    Location orientedLocation = position.clone();
                    orientedLocation.setYaw(yaw);
                    orientedLocation.setPitch(pitch);
                    
                    droneEntity.teleport(orientedLocation);
                }
            }
            
            @Override
            public void onPathCompleted() {
                player.sendMessage("§a实体无人机已到达目标位置！");
                // 可以在这里添加到达效果，比如粒子爆炸等
            }
            
            @Override
            public void onPathStopped() {
                player.sendMessage("§c实体无人机运动已停止");
            }
        };
        
        startRealtimeDrone(path, speed, smoothing, player, callback);
    }
    
    /**
     * 工具方法：根据距离和速度计算合适的速度
     */
    public static double calculateOptimalSpeed(List<Location> path, double desiredDurationSeconds) {
        double pathLength = PathSmoothingUtil.calculatePathLength(path);
        return pathLength / desiredDurationSeconds;
    }
}
