package org.etwxr9.frostcraftbu.Module;

import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.etwxr9.buildingunit.BuildingUnitAPI;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Module.ContainerModule.ContainerModuleSaveData;
import org.etwxr9.frostcraftbu.Module.ContainerModule.ContainerUnit;
import org.etwxr9.frostcraftbu.Module.Behavior.ICharge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class ChargeModule extends BaseModule implements ICharge {

    private Location chargePointLoc;
    private String chargePointUUID;

    public ChargeModule(FCBuilding building) {
        super(building);
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ChargeModuleSaveData extends BaseModuleSaveData {
        private String chargePointLoc;
        private String uuid;

        public String getChargePointLoc() {
            return chargePointLoc;
        }

        public String getUuid() {
            return uuid;
        }

        public ChargeModuleSaveData() {
        }

        public ChargeModuleSaveData(String uuid, Location loc) {
            this.uuid = uuid;
            this.chargePointLoc = loc.toString();
        }

        @Override
        public String getModuleTypeId() {
            return ModuleManager.ModuleType.ContainerModule.getName();
        }

    }

    @Override
    public BaseModuleSaveData getSaveData() {
        // Convert HashMap to ArrayList, sorting by slotId
        return new ChargeModuleSaveData(chargePointUUID, chargePointLoc);
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ContainerModuleSaveData) {
            ChargeModuleSaveData saveData = (ChargeModuleSaveData) data;
            chargePointUUID = saveData.getUuid();
            chargePointLoc = locFromString(saveData.chargePointLoc);
        }
    }

    private Location locFromString(String locS) {
        // loc string example: Location{world=null,x=1.0,y=2.0,z=3.0,pitch=5.0,yaw=4.0}
        var worldName = locS.split(",")[0].split("=")[1];
        var x = Double.parseDouble(locS.split(",")[1].split("=")[1]);
        var y = Double.parseDouble(locS.split(",")[2].split("=")[1]);
        var z = Double.parseDouble(locS.split(",")[3].split("=")[1]);
        return new Location(Bukkit.getWorld(worldName), x, y, z);
    }

    @Override
    public ArmorStand getChargePoint() {
        var armorStand = Bukkit.getEntity(UUID.fromString(chargePointUUID));
        if (armorStand == null) {
            chargePointLoc.getChunk().load();
            armorStand = Bukkit.getEntity(UUID.fromString(chargePointUUID));
        }
        return (ArmorStand) armorStand;
    }

    @Override
    public Location getChargeLoc() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getChargeLoc'");
    }

    @Override
    public void onLoad(boolean frist) {
        if (!frist) {
            return;
        }
        var unit = BuildingUnitAPI.getUnit(building.getUnitUUID());
        var bb = unit.getBoundingBox();
        var entities = unit.getWorld().getNearbyEntities(bb);
        var armorStandOpt = entities.stream().filter(e -> {
            return e.getType().equals(EntityType.ARMOR_STAND);
        }).findAny();
        if (armorStandOpt.isPresent()) {
            chargePointUUID = armorStandOpt.get().getUniqueId().toString();
            chargePointLoc = armorStandOpt.get().getLocation();
        }
    }

    @Override
    public void onUnload() {
    }

    @Override
    public String getModuleTypeId() {
        return ModuleManager.ModuleType.ChargeModule.getName();
    }

}
