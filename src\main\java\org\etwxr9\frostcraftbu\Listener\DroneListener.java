package org.etwxr9.frostcraftbu.Listener;

import org.bukkit.entity.Interaction;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.persistence.PersistentDataType;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Manager.DroneManager;

import net.kyori.adventure.text.TextComponent;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;

public class DroneListener implements Listener {

    @EventHandler
    public void handleDroneItemUse(PlayerInteractEvent e) {

        if (e.getItem() == null) {
            return;
        }
        var droneItemTemp = ItemManager.getItem(FrostCraftBU.i().getConfig().getString("Drone.DroneItemId", "无人机"));
        // 仅比较名称
        String itemName = PlainTextComponentSerializer.plainText().serialize(droneItemTemp.displayName());
        String heldItemName = PlainTextComponentSerializer.plainText().serialize(e.getItem().displayName());
        // log
        FrostCraftBU.i().getLogger().info("无人机物品名称: " + itemName);
        FrostCraftBU.i().getLogger().info("手持物品名称: " + heldItemName);
        if (!heldItemName.equals(itemName)) {
            return;
        }
        e.setCancelled(true);
        DroneManager.i().getDroneItemUI(e.getItem()).open(e.getPlayer());

    }

    @EventHandler
    public void handleDroneEntityInteract(PlayerInteractEntityEvent e) {
        if (!DroneManager.i().isInteraction(e.getRightClicked())) {
            return;
        }
        e.setCancelled(true);
        DroneManager.i().getDroneFromInteraction((Interaction) e.getRightClicked()).ifPresent(
                (d) -> {
                    DroneManager.i().getDroneUI(d).open(e.getPlayer());
                });

    }
}
