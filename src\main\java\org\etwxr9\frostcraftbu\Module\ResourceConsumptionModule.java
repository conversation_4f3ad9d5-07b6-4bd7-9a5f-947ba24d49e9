package org.etwxr9.frostcraftbu.Module;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import org.bukkit.Bukkit;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Module.Behavior.IResourceConsumption;
import org.etwxr9.frostcraftbu.Recipe.ConsumeRecipeConfig;
import org.etwxr9.frostcraftbu.Recipe.RecipeManager;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class ResourceConsumptionModule extends BaseModule implements IResourceConsumption {

    // 配置数据类
    private class ProductionLineConfig {
        List<Integer> inputSlots;
        List<String> availableRecipes;
    }

    // 运行时状态类
    private class ProductionLineState {
        String currentRecipe;
        int progress;
    }

    // 产线类，组合配置和状态
    private class ProductionLine {
        ProductionLineConfig config;
        ProductionLineState state;

        ProductionLine(ProductionLineConfig config) {
            this.config = config;
            this.state = new ProductionLineState();
            this.state.currentRecipe = null;
            this.state.progress = 0;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResourceConsumptionModuleSaveData extends BaseModuleSaveData {
        private static class ProductionLineStateSaveData {
            private String currentRecipe;
            private int progress;

            public ProductionLineStateSaveData() {
            }

            public ProductionLineStateSaveData(ProductionLineState state) {
                this.currentRecipe = state.currentRecipe;
                this.progress = state.progress;
            }
        }

        private List<ProductionLineStateSaveData> productionLineStates;

        public ResourceConsumptionModuleSaveData() {
        }

        public ResourceConsumptionModuleSaveData(List<ProductionLineStateSaveData> states) {
            this.productionLineStates = states;
        }

        public List<ProductionLineStateSaveData> getProductionLineStates() {
            return productionLineStates;
        }

        @Override
        public String getModuleTypeId() {
            return ModuleManager.ModuleType.ResourceConsumptionModule.getName();
        }
    }

    private List<ProductionLine> productionLines = new ArrayList<>();
    private ContainerModule containerModule;

    public ResourceConsumptionModule(FCBuilding building) {
        super(building);
    }

    @Override
    public void onLoad(boolean first) {
        containerModule = (ContainerModule) building.getModule("container");
        if (containerModule == null) {
            throw new IllegalStateException("ResourceConsumptionModule requires ContainerModule");
        }

        // 从配置加载产线配置
        List<Map<?, ?>> lines = getModuleConfig().getMapList("lines");
        for (Map<?, ?> line : lines) {
            ProductionLineConfig config = new ProductionLineConfig();
            config.inputSlots = (List<Integer>) line.get("input");
            config.availableRecipes = (List<String>) line.get("recipe");

            productionLines.add(new ProductionLine(config));
        }
    }

    @Override
    public void tick() {
        for (int i = 0; i < productionLines.size(); i++) {
            ProductionLine line = productionLines.get(i);
            if (line.state.currentRecipe == null)
                continue;

            if (canProduce(i)) {
                ConsumeRecipeConfig recipe = RecipeManager.i().getConsumeRecipe(line.state.currentRecipe);
                line.state.progress++;

                if (line.state.progress >= recipe.getTime()) {
                    produce(i);
                    line.state.progress = 0;
                }
            }
        }
    }

    private void produce(int lineIndex) {
        ProductionLine line = productionLines.get(lineIndex);
        ConsumeRecipeConfig recipe = RecipeManager.i().getConsumeRecipe(line.state.currentRecipe);

        // 消耗输入物品
        for (Map.Entry<String, Integer> input : recipe.getInput().entrySet()) {
            int remaining = input.getValue();
            for (int slotId : line.config.inputSlots) {
                if (remaining <= 0)
                    break;
                String slotItemId = containerModule.getSlotItemId(slotId);
                if (input.getKey().equals(slotItemId)) {
                    var gainSteamobj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamObj");
                    var gainSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamEntry");
                    var gainSteamValue = recipe.getScoreboardValue();
                    var gainSteamMax = FrostCraftBU.i().getConfig().getInt("ScoreboardObjectives.SteamMax");
                    Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                    Objective objective = scoreboard.getObjective(gainSteamobj);
                    Score score = objective.getScore(gainSteamEntry);
                    score.setScore(score.getScore() + gainSteamValue);
                    int slotCount = containerModule.getSlotItemCount(slotId);
                    int consume = Math.min(remaining, slotCount);
                    containerModule.setSlotItem(slotId, slotItemId, slotCount - consume);
                }
            }
        }
        // 回调
        consumeCallback(line.state.currentRecipe);
    }

    @Override
    public boolean setRecipe(int lineIndex, String recipeId) {
        if (lineIndex >= productionLines.size())
            return false;

        ProductionLine line = productionLines.get(lineIndex);
        if (!line.config.availableRecipes.contains(recipeId))
            return false;

        line.state.currentRecipe = recipeId;
        line.state.progress = 0;

        // 更新容器slot的物品类型
        if (recipeId != null) {
            ConsumeRecipeConfig recipe = RecipeManager.i().getConsumeRecipe(recipeId);
            // 设置输入槽
            for (int i = 0; i < line.config.inputSlots.size(); i++) {
                int slotId = line.config.inputSlots.get(i);
                if (i < recipe.getInput().size()) {
                    String itemId = new ArrayList<>(recipe.getInput().keySet()).get(i);
                    containerModule.setSlotItem(slotId, itemId, 0);
                }
            }
        } else {
            for (int slotId : line.config.inputSlots) {
                containerModule.clearSlot(slotId);
            }
        }
        return true;
    }

    @Override
    public String getRecipe(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return null;
        return productionLines.get(lineIndex).state.currentRecipe;
    }

    @Override
    public int getProgress(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return 0;
        return productionLines.get(lineIndex).state.progress;
    }

    @Override
    public boolean canProduce(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return false;

        ProductionLine line = productionLines.get(lineIndex);
        if (line.state.currentRecipe == null)
            return false;

        ConsumeRecipeConfig recipe = RecipeManager.i().getConsumeRecipe(line.state.currentRecipe);

        if (recipe == null) {
            return false;
        }

        // 检查输入物品是否足够
        for (Map.Entry<String, Integer> input : recipe.getInput().entrySet()) {
            int total = 0;
            for (int slotId : line.config.inputSlots) {
                if (input.getKey().equals(containerModule.getSlotItemId(slotId))) {
                    total += containerModule.getSlotItemCount(slotId);
                }
            }
            if (total < input.getValue())
                return false;
        }

        // 检查记分板是否满
        var gainSteamobj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamObj");
        var gainSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamEntry");
        var gainSteamValue = recipe.getScoreboardValue();
        var gainSteamMax = FrostCraftBU.i().getConfig().getInt("ScoreboardObjectives.SteamMax");
        if (gainSteamobj != null && gainSteamEntry != null) {
            try {
                Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                Objective objective = scoreboard.getObjective(gainSteamobj);
                if (objective != null) {
                    Score score = objective.getScore(gainSteamEntry);
                    if (gainSteamMax > 0 && score.getScore() + gainSteamValue >= gainSteamMax) {
                        return false;
                    }
                } else {
                    FrostCraftBU.i().getLogger().warning("记分板目标 '" + gainSteamobj + "' 不存在！请在游戏中创建它。");
                }
            } catch (Exception e) {
                FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + gainSteamobj + "): ", e);
            }
        }

        return true;
    }

    @Override
    public BaseModuleSaveData getSaveData() {
        List<ResourceConsumptionModuleSaveData.ProductionLineStateSaveData> saveStates = new ArrayList<>();
        for (ProductionLine line : productionLines) {
            saveStates.add(new ResourceConsumptionModuleSaveData.ProductionLineStateSaveData(line.state));
        }
        return new ResourceConsumptionModuleSaveData(saveStates);
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ResourceConsumptionModuleSaveData) {
            ResourceConsumptionModuleSaveData saveData = (ResourceConsumptionModuleSaveData) data;
            List<ResourceConsumptionModuleSaveData.ProductionLineStateSaveData> states = saveData
                    .getProductionLineStates();

            // 确保状态数量与产线数量匹配
            for (int i = 0; i < Math.min(states.size(), productionLines.size()); i++) {
                ResourceConsumptionModuleSaveData.ProductionLineStateSaveData stateSaveData = states.get(i);
                ProductionLine line = productionLines.get(i);
                line.state.currentRecipe = stateSaveData.currentRecipe;
                line.state.progress = stateSaveData.progress;
            }
        }
    }

    @Override
    public void onUnload() {
    }

    @Override
    public String getModuleTypeId() {
        return ModuleManager.ModuleType.ResourceConsumptionModule.getName();
    }

    @Override
    public List<String> getAvailableRecipes(int lineIndex) {
        var arBuilding = productionLines.get(lineIndex).config.availableRecipes;
        var arTech = TechTreeManager.i().getUnlockedConsumeRecipes();
        arBuilding.retainAll(arTech);
        return arBuilding;
    }

    @Override
    public int getLineCount() {
        return productionLines.size();
    }

    @Override
    public void consumeCallback(String recipeId) {

    }
}