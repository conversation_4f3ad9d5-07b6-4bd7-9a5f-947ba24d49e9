package org.etwxr9.frostcraftbu.pathfinding.visualizer;

import de.bsommerfeld.pathetic.api.wrapper.PathPosition;
import de.bsommerfeld.pathetic.api.pathing.result.Path;
import de.bsommerfeld.pathetic.bukkit.mapper.BukkitMapper;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.entity.Display;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.plugin.Plugin;
import org.bukkit.util.Transformation;
import org.joml.AxisAngle4f;
import org.joml.Vector3f;

import java.util.ArrayList;
import java.util.List;

/**
 * 3D向量类，用于曲线计算
 */
class Vector3D {
    public final double x, y, z;

    public Vector3D(double x, double y, double z) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    public Vector3D add(Vector3D other) {
        return new Vector3D(x + other.x, y + other.y, z + other.z);
    }

    public Vector3D subtract(Vector3D other) {
        return new Vector3D(x - other.x, y - other.y, z - other.z);
    }

    public Vector3D multiply(double scalar) {
        return new Vector3D(x * scalar, y * scalar, z * scalar);
    }

    public double length() {
        return Math.sqrt(x * x + y * y + z * z);
    }

    public Vector3D normalize() {
        double len = length();
        if (len == 0) return new Vector3D(0, 0, 0);
        return new Vector3D(x / len, y / len, z / len);
    }
}

/**
 * Catmull-Rom样条曲线实现，用于路径平滑
 */
class CatmullRomSpline {
    private final List<Vector3D> controlPoints;

    public CatmullRomSpline(List<Vector3D> points) {
        this.controlPoints = new ArrayList<>(points);
    }

    /**
     * 在指定参数t处计算样条曲线上的点
     * @param t 参数，范围[0, controlPoints.size() - 1]
     * @return 曲线上的点
     */
    public Vector3D getPoint(double t) {
        if (controlPoints.size() < 2) {
            return controlPoints.isEmpty() ? new Vector3D(0, 0, 0) : controlPoints.get(0);
        }

        // 确保t在有效范围内
        t = Math.max(0, Math.min(controlPoints.size() - 1, t));

        int segment = (int) Math.floor(t);
        double localT = t - segment;

        // 获取四个控制点
        Vector3D p0 = getControlPoint(segment - 1);
        Vector3D p1 = getControlPoint(segment);
        Vector3D p2 = getControlPoint(segment + 1);
        Vector3D p3 = getControlPoint(segment + 2);

        // Catmull-Rom样条公式
        double t2 = localT * localT;
        double t3 = t2 * localT;

        Vector3D result = p0.multiply(-0.5 * t3 + t2 - 0.5 * localT)
                .add(p1.multiply(1.5 * t3 - 2.5 * t2 + 1))
                .add(p2.multiply(-1.5 * t3 + 2 * t2 + 0.5 * localT))
                .add(p3.multiply(0.5 * t3 - 0.5 * t2));

        return result;
    }

    /**
     * 获取控制点，处理边界情况
     */
    private Vector3D getControlPoint(int index) {
        if (index < 0) {
            // 延伸第一个点
            Vector3D p0 = controlPoints.get(0);
            Vector3D p1 = controlPoints.get(1);
            return p0.multiply(2).subtract(p1);
        } else if (index >= controlPoints.size()) {
            // 延伸最后一个点
            int lastIndex = controlPoints.size() - 1;
            Vector3D p0 = controlPoints.get(lastIndex - 1);
            Vector3D p1 = controlPoints.get(lastIndex);
            return p1.multiply(2).subtract(p0);
        } else {
            return controlPoints.get(index);
        }
    }

    /**
     * 生成平滑的路径点
     * @param resolution 每个段的分辨率
     * @return 平滑后的路径点列表
     */
    public List<Vector3D> generateSmoothPath(int resolution) {
        List<Vector3D> smoothPath = new ArrayList<>();

        if (controlPoints.size() < 2) {
            return new ArrayList<>(controlPoints);
        }

        double maxT = controlPoints.size() - 1;
        double step = maxT / (resolution * (controlPoints.size() - 1));

        for (double t = 0; t <= maxT; t += step) {
            smoothPath.add(getPoint(t));
        }

        // 确保包含最后一个点
        if (!smoothPath.isEmpty()) {
            smoothPath.add(getPoint(maxT));
        }

        return smoothPath;
    }
}

/**
 * 无人机可视化系统，使用ItemDisplay实体来展示无人机并实现路径跟踪动画
 */
public class DroneVisualizer {

    private final Plugin plugin;
    private final List<ItemDisplay> activeDisplays;

    public DroneVisualizer(Plugin plugin) {
        this.plugin = plugin;
        this.activeDisplays = new ArrayList<>();
    }

    /**
     * 创建无人机ItemDisplay实体
     */
    public ItemDisplay createDroneDisplay(Location location) {
        World world = location.getWorld();
        if (world == null) {
            return null;
        }

        // 创建ItemDisplay实体
        ItemDisplay droneDisplay = world.spawn(location, ItemDisplay.class);

        // 设置无人机外观（使用铁锭作为无人机模型）
        ItemStack droneItem = new ItemStack(Material.IRON_BLOCK);
        droneDisplay.setItemStack(droneItem);

        // 设置显示属性
        droneDisplay.setBillboard(Display.Billboard.CENTER);
        droneDisplay.setViewRange(100.0f);
        droneDisplay.setGlowing(true);
        droneDisplay.setTeleportDuration(1);

        // 设置变换（缩放和旋转）
        Transformation transformation = new Transformation(
                new Vector3f(0, 0, 0), // 平移
                new AxisAngle4f(0, 0, 1, 0), // 左手旋转
                new Vector3f(1f, 1f, 1f), // 缩放
                new AxisAngle4f(0, 0, 1, 0) // 右手旋转
        );
        droneDisplay.setTransformation(transformation);

        // 添加到活动显示列表
        activeDisplays.add(droneDisplay);

        return droneDisplay;
    }

    /**
     * 沿路径移动无人机，实现平滑动画
     */
    public void animateDroneAlongPath(Path path, World world, Player player) {
        animateDroneAlongPath(path, world, player, true, 10);
    }

    /**
     * 沿路径移动无人机，实现平滑动画（使用Location列表）
     */
    public void animateDroneAlongPath(List<Location> path, World world, Player player, boolean useSmoothing, int smoothingResolution) {
        if (path == null || path.isEmpty()) {
            player.sendMessage("路径为空，无法创建动画");
            return;
        }

        // 生成平滑路径
        List<Vector3D> smoothPath = null;
        if (useSmoothing && path.size() > 2) {
            // 转换为Vector3D列表
            List<Vector3D> controlPoints = new ArrayList<>();
            for (Location loc : path) {
                controlPoints.add(new Vector3D(loc.getX(), loc.getY(), loc.getZ()));
            }

            // 创建样条曲线并生成平滑路径
            CatmullRomSpline spline = new CatmullRomSpline(controlPoints);
            smoothPath = spline.generateSmoothPath(smoothingResolution);

            player.sendMessage("§a使用曲线平滑，原始路径: " + path.size() + " 点，平滑后: " + smoothPath.size() + " 点");
        } else {
            player.sendMessage("§e使用线性插值，路径长度: " + path.size());
        }

        // 在起点创建无人机
        Location startLocation = path.get(0);
        ItemDisplay droneDisplay = createDroneDisplay(startLocation);

        if (droneDisplay == null) {
            player.sendMessage("无法创建无人机显示实体");
            return;
        }

        // 显示原始路径点
        for (Location loc : path) {
            player.sendBlockChange(loc, Material.YELLOW_STAINED_GLASS.createBlockData());
        }

        // 创建动画任务
        if (useSmoothing && smoothPath != null) {
            new SmoothDroneAnimationTask(droneDisplay, smoothPath, world, player).runTaskTimer(plugin, 0L, 1L);
        } else {
            // 转换Location列表为PathPosition数组用于线性动画
            PathPosition[] pathArray = path.stream()
                .map(loc -> new PathPosition(loc.getX() - 0.5, loc.getY() - 0.5, loc.getZ() - 0.5))
                .toArray(PathPosition[]::new);
            new DroneAnimationTask(droneDisplay, pathArray, world, player).runTaskTimer(plugin, 0L, 1L);
        }
    }

    /**
     * 沿路径移动无人机，实现平滑动画
     * @param path 路径
     * @param world 世界
     * @param player 玩家
     * @param useSmoothing 是否使用曲线平滑
     * @param smoothingResolution 平滑分辨率（每个路径段的插值点数）
     */
    public void animateDroneAlongPath(Path path, World world, Player player, boolean useSmoothing, int smoothingResolution) {
        if (path.length() == 0) {
            player.sendMessage("路径为空，无法创建动画");
            return;
        }

        // 将Path转换为PathPosition数组以便处理
        PathPosition[] pathArray = convertPathToArray(path);

        // 生成平滑路径
        List<Vector3D> smoothPath = null;
        if (useSmoothing && pathArray.length > 2) {
            // 转换为Vector3D列表
            List<Vector3D> controlPoints = new ArrayList<>();
            for (PathPosition pos : pathArray) {
                controlPoints.add(new Vector3D(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5));
            }

            // 创建样条曲线并生成平滑路径
            CatmullRomSpline spline = new CatmullRomSpline(controlPoints);
            smoothPath = spline.generateSmoothPath(smoothingResolution);

            player.sendMessage("§a使用曲线平滑，原始路径: " + pathArray.length + " 点，平滑后: " + smoothPath.size() + " 点");
        } else {
            player.sendMessage("§e使用线性插值，路径长度: " + pathArray.length);
        }

        // 在起点创建无人机
        Location startLocation = BukkitMapper.toLocation(pathArray[0], world);
        ItemDisplay droneDisplay = createDroneDisplay(startLocation);

        if (droneDisplay == null) {
            player.sendMessage("无法创建无人机显示实体");
            return;
        }

        // 显示原始路径点
        path.forEach(position -> {
            Location loc = BukkitMapper.toLocation(position, world);
            player.sendBlockChange(loc, Material.YELLOW_STAINED_GLASS.createBlockData());
        });

        // 创建动画任务
        if (useSmoothing && smoothPath != null) {
            new SmoothDroneAnimationTask(droneDisplay, smoothPath, world, player).runTaskTimer(plugin, 0L, 1L);
        } else {
            new DroneAnimationTask(droneDisplay, pathArray, world, player).runTaskTimer(plugin, 0L, 1L);
        }
    }

    /**
     * 将Path转换为PathPosition数组
     */
    private PathPosition[] convertPathToArray(Path path) {
        List<PathPosition> pathList = new ArrayList<>();
        path.forEach(pathList::add);
        return pathList.toArray(new PathPosition[0]);
    }

    /**
     * 清理所有活动的显示实体
     */
    public void cleanup() {
        for (ItemDisplay display : activeDisplays) {
            if (display != null && !display.isDead()) {
                display.remove();
            }
        }
        activeDisplays.clear();
    }

    /**
     * 平滑无人机动画任务（使用样条曲线）
     */
    private class SmoothDroneAnimationTask extends BukkitRunnable {
        private final ItemDisplay droneDisplay;
        private final List<Vector3D> smoothPath;
        private final World world;
        private final Player player;
        private int currentIndex;
        private static final int ANIMATION_SPEED = 2; // 每tick移动的点数

        public SmoothDroneAnimationTask(ItemDisplay droneDisplay, List<Vector3D> smoothPath, World world, Player player) {
            this.droneDisplay = droneDisplay;
            this.smoothPath = smoothPath;
            this.world = world;
            this.player = player;
            this.currentIndex = 0;
        }

        @Override
        public void run() {
            if (droneDisplay == null || droneDisplay.isDead()) {
                cancel();
                return;
            }

            if (currentIndex >= smoothPath.size()) {
                // 动画完成
                player.sendMessage("§a无人机已到达目标位置");

                // 5秒后移除显示实体
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        droneDisplay.remove();
                        activeDisplays.remove(droneDisplay);
                    }
                }.runTaskLater(plugin, 100L);

                cancel();
                return;
            }

            // 获取当前位置
            Vector3D currentPos = smoothPath.get(currentIndex);

            // 更新无人机位置
            Location newLocation = new Location(world, currentPos.x, currentPos.y, currentPos.z);
            droneDisplay.teleport(newLocation);

            // 计算旋转（让无人机面向移动方向）
            // if (currentIndex < smoothPath.size() - 1) {
            //     Vector3D nextPos = smoothPath.get(currentIndex + 1);
            //     Vector3D direction = nextPos.subtract(currentPos).normalize();

            //     if (direction.length() > 0.01) { // 避免除零错误
            //         float yaw = (float) Math.toDegrees(Math.atan2(-direction.x, direction.z));
            //         float pitch = (float) Math.toDegrees(Math.asin(-direction.y));

            //         // 更新变换以包含旋转
            //         Transformation transformation = new Transformation(
            //             new Vector3f(0, 0, 0),
            //             new AxisAngle4f((float) Math.toRadians(yaw), 0, 1, 0),
            //             new Vector3f(1f, 1f, 1f),
            //             new AxisAngle4f((float) Math.toRadians(pitch), 1, 0, 0)
            //         );
            //         droneDisplay.setTransformation(transformation);
            //     }
            // }

            // 移动到下一个点
            currentIndex += ANIMATION_SPEED;
        }
    }

    /**
     * 无人机动画任务（线性插值）
     */
    private class DroneAnimationTask extends BukkitRunnable {
        private final ItemDisplay droneDisplay;
        private final PathPosition[] path;
        private final World world;
        private final Player player;
        private int currentIndex;
        private int interpolationStep;
        private static final int STEPS_PER_SEGMENT = 5; // 每段路径的插值步数

        public DroneAnimationTask(ItemDisplay droneDisplay, PathPosition[] path, World world, Player player) {
            this.droneDisplay = droneDisplay;
            this.path = path;
            this.world = world;
            this.player = player;
            this.currentIndex = 0;
            this.interpolationStep = 0;
        }

        @Override
        public void run() {
            if (droneDisplay == null || droneDisplay.isDead()) {
                cancel();
                return;
            }

            if (currentIndex >= path.length - 1) {
                // 动画完成
                player.sendMessage("无人机已到达目标位置");

                // 5秒后移除显示实体
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        droneDisplay.remove();
                        activeDisplays.remove(droneDisplay);
                    }
                }.runTaskLater(plugin, 100L);

                cancel();
                return;
            }

            // 计算当前位置和下一个位置
            PathPosition currentPos = path[currentIndex];
            PathPosition nextPos = path[currentIndex + 1];

            // 线性插值计算中间位置
            double progress = (double) interpolationStep / STEPS_PER_SEGMENT;

            double x = currentPos.getX() + (nextPos.getX() - currentPos.getX()) * progress;
            double y = currentPos.getY() + (nextPos.getY() - currentPos.getY()) * progress;
            double z = currentPos.getZ() + (nextPos.getZ() - currentPos.getZ()) * progress;

            // 更新无人机位置
            Location newLocation = new Location(world, x + 0.5, y + 0.5, z + 0.5);
            droneDisplay.teleport(newLocation);

            // // 计算旋转（让无人机面向移动方向）
            // Vector direction = new Vector(
            // nextPos.getX() - currentPos.getX(),
            // 0, // 不考虑Y轴旋转，保持水平
            // nextPos.getZ() - currentPos.getZ()
            // ).normalize();

            // if (direction.lengthSquared() > 0) {
            // float yaw = (float) Math.toDegrees(Math.atan2(-direction.getX(),
            // direction.getZ()));

            // // 更新变换以包含旋转
            // Transformation transformation = new Transformation(
            // new Vector3f(0, 0, 0),
            // new AxisAngle4f((float) Math.toRadians(yaw), 0, 1, 0),
            // new Vector3f(1f, 1f, 1f),
            // new AxisAngle4f(0, 0, 1, 0)
            // );
            // droneDisplay.setTransformation(transformation);
            // }

            // 更新插值步数
            interpolationStep++;
            if (interpolationStep >= STEPS_PER_SEGMENT) {
                interpolationStep = 0;
                currentIndex++;
            }
        }
    }
}
