package org.etwxr9.frostcraftbu.pathfinding;

import org.bukkit.plugin.java.JavaPlugin;
import org.etwxr9.frostcraftbu.pathfinding.command.DroneCommand;
import org.etwxr9.frostcraftbu.pathfinding.listener.ChunkInvalidateListener;

public final class PatheticPlugin {

    private DroneCommand droneCommand;

    public void enable(JavaPlugin p) {

        // 注意：Pathetic库的初始化现在由主插件FrostCraftBU负责
        // 这里只需要注册命令和监听器

        // Register the command executor for the "drone" command
        droneCommand = new DroneCommand(p);
        p.getCommand("drone").setExecutor(droneCommand);

        // Register the ChunkInvalidateListener
        p.getServer().getPluginManager().registerEvents(new ChunkInvalidateListener(), p);
    }

}
