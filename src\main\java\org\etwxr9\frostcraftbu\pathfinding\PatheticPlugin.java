package org.etwxr9.frostcraftbu.pathfinding;

import de.bsommerfeld.pathetic.api.factory.PathfinderFactory;
import de.bsommerfeld.pathetic.api.factory.PathfinderInitializer;
import de.bsommerfeld.pathetic.api.pathing.Pathfinder;
import de.bsommerfeld.pathetic.api.pathing.configuration.PathfinderConfiguration;
import de.bsommerfeld.pathetic.bukkit.PatheticBukkit;
import de.bsommerfeld.pathetic.bukkit.initializer.BukkitPathfinderInitializer;
import de.bsommerfeld.pathetic.bukkit.provider.LoadingNavigationPointProvider;
import de.bsommerfeld.pathetic.engine.factory.AStarPathfinderFactory;

import org.bukkit.plugin.java.JavaPlugin;
import org.etwxr9.frostcraftbu.pathfinding.command.DroneCommand;
import org.etwxr9.frostcraftbu.pathfinding.command.PatheticCommand;
import org.etwxr9.frostcraftbu.pathfinding.listener.ChunkInvalidateListener;
import org.etwxr9.frostcraftbu.pathfinding.processor.DroneCostProcessor;
import org.etwxr9.frostcraftbu.pathfinding.processor.DroneValidationProcessor;

import java.util.List;

public final class PatheticPlugin extends JavaPlugin {

    private DroneCommand droneCommand;

    // Called when the plugin is enabled
    @Override
    public void onEnable() {

        // Initialize Pathetic with this plugin instance
        PatheticBukkit.initialize(this);

        // Create the respective PathfinderFactory
        PathfinderFactory factory = new AStarPathfinderFactory();

        // Some pathfinders need specific initialization
        // For example Bukkit pathfinders need a BukkitPathfinderInitializer
        PathfinderInitializer initializer = new BukkitPathfinderInitializer();
        // Create custom configuration for the drone pathfinder
        PathfinderConfiguration droneConfiguration = PathfinderConfiguration.builder()
                .provider(new LoadingNavigationPointProvider()) // For loading chunks
                .fallback(true) // Allow fallback strategies if the primary fails
                .nodeValidationProcessors(List.of(new DroneValidationProcessor()))
                .nodeCostProcessors(List.of(new DroneCostProcessor()))
                .maxIterations(150000) // Higher iteration count for 3D pathfinding
                .build();

        // Create the drone pathfinding instance
        Pathfinder dronePathfinder = factory.createPathfinder(droneConfiguration, initializer);

        // Register the command executor for the "drone" command
        droneCommand = new DroneCommand(dronePathfinder, this);
        getCommand("drone").setExecutor(droneCommand);

        // Register the ChunkInvalidateListener
        getServer().getPluginManager().registerEvents(new ChunkInvalidateListener(), this);
    }

    // Called when the plugin is disabled
    @Override
    public void onDisable() {
        // Clean up drone command resources
        if (droneCommand != null) {
            droneCommand.cleanup();
        }
    }
}
