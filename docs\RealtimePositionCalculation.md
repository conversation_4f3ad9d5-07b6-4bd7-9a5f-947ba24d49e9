# 实时位置计算功能文档

## 概述

新增的实时位置计算功能允许根据无人机的运行时间、速度和路径来动态计算无人机在任意时刻的精确位置。这解决了预计算路径分辨率与实际运动速度不匹配的问题。

## 核心功能

### 主要方法

#### calculateRealtimePosition()
```java
public static Location calculateRealtimePosition(int elapsedTicks, double speedMetersPerSecond, 
                                               List<Location> rawPath, SmoothingType smoothingType)
```

**参数说明：**
- `elapsedTicks`: 无人机已运行的tick数
- `speedMetersPerSecond`: 无人机速度（米/秒）
- `rawPath`: 原始路径点列表
- `smoothingType`: 平滑算法类型

**返回值：**
- 计算出的当前位置，如果路径已完成则返回null

### 辅助方法

#### isPathCompleted()
检查无人机是否已完成整个路径
```java
public static boolean isPathCompleted(int elapsedTicks, double speedMetersPerSecond, List<Location> rawPath)
```

#### calculatePathDurationTicks()
计算完成整个路径需要的时间（tick）
```java
public static int calculatePathDurationTicks(double speedMetersPerSecond, List<Location> rawPath)
```

#### calculatePathProgress()
计算路径进度百分比（0.0-1.0）
```java
public static double calculatePathProgress(int elapsedTicks, double speedMetersPerSecond, List<Location> rawPath)
```

#### calculateMovementDirection()
计算当前的运动方向向量
```java
public static Vector3D calculateMovementDirection(int elapsedTicks, double speedMetersPerSecond, 
                                                 List<Location> rawPath, SmoothingType smoothingType)
```

## 算法原理

### 1. 距离计算
根据已运行时间和速度计算已行进的距离：
```
已行进距离 = (已运行tick数 / 20) × 速度(米/秒)
```

### 2. 位置定位
根据不同的平滑算法在路径上定位当前位置：

#### 线性插值（LINEAR）
- 直接在原始路径段上进行线性插值
- 性能最好，适用于简单路径

#### Catmull-Rom样条（CATMULL_ROM）
- 先生成平滑路径，再在平滑路径上定位
- 提供自然的曲线运动

#### 贝塞尔曲线（BEZIER）
- 使用贝塞尔曲线算法生成平滑路径
- 适用于需要特殊曲线效果的场景

### 3. 边界处理
- 如果已行进距离超过路径总长度，返回终点位置
- 如果已行进距离为0或负数，返回起点位置

## 使用示例

### 基本用法
```java
// 假设无人机已运行100tick，速度5米/秒
int elapsedTicks = 100;
double speed = 5.0;
List<Location> path = getPathFromSomewhere();

Location currentPosition = PathSmoothingUtil.calculateRealtimePosition(
    elapsedTicks, speed, path, PathSmoothingUtil.SmoothingType.CATMULL_ROM);

if (currentPosition != null) {
    // 更新无人机位置
    drone.teleport(currentPosition);
}
```

### 完整的实时运动系统
```java
public class RealtimeDroneController extends BukkitRunnable {
    private final List<Location> path;
    private final double speed;
    private final Entity droneEntity;
    private int elapsedTicks = 0;
    
    @Override
    public void run() {
        // 检查是否完成
        if (PathSmoothingUtil.isPathCompleted(elapsedTicks, speed, path)) {
            cancel();
            return;
        }
        
        // 计算当前位置
        Location position = PathSmoothingUtil.calculateRealtimePosition(
            elapsedTicks, speed, path, PathSmoothingUtil.SmoothingType.CATMULL_ROM);
        
        // 计算运动方向
        PathSmoothingUtil.Vector3D direction = PathSmoothingUtil.calculateMovementDirection(
            elapsedTicks, speed, path, PathSmoothingUtil.SmoothingType.CATMULL_ROM);
        
        // 更新实体位置和朝向
        if (position != null && direction != null) {
            float yaw = (float) Math.toDegrees(Math.atan2(-direction.x, direction.z));
            position.setYaw(yaw);
            droneEntity.teleport(position);
        }
        
        elapsedTicks++;
    }
}
```

## 性能考虑

### 1. 算法复杂度
- **线性插值**: O(n) - 需要遍历路径段找到当前位置
- **样条曲线**: O(n×r) - 需要先生成平滑路径，r为分辨率
- **贝塞尔曲线**: O(n×r) - 类似样条曲线

### 2. 优化建议
- 对于长路径，考虑使用线性插值以提高性能
- 缓存生成的平滑路径，避免重复计算
- 对于高频率更新，考虑降低更新频率或使用插值

### 3. 内存使用
- 线性插值：最少内存使用
- 样条和贝塞尔：需要额外内存存储平滑路径点

## 测试功能

### 命令测试
使用测试命令验证功能：
```
/frostcraftbu dronetest realtime [distance] [speed] [smoothing]
```

**示例：**
```
/frostcraftbu dronetest realtime 100 7.5 catmull
```
- 距离：100格
- 速度：7.5米/秒
- 平滑：Catmull-Rom样条

### 测试特性
- 实时显示运动进度
- 粒子效果跟踪（可选）
- 性能统计
- 路径完成检测

## 实际应用场景

### 1. 无人机实体控制
- 精确控制无人机实体的位置
- 平滑的运动轨迹
- 自然的转向效果

### 2. 粒子效果系统
- 沿路径生成连续的粒子效果
- 可控的粒子密度和频率

### 3. 摄像机跟踪
- 平滑的摄像机运动
- 电影级的镜头效果

### 4. 物理模拟
- 真实的物理运动模拟
- 可变速度控制

## 注意事项

### 1. 坐标系统
- 所有计算基于Bukkit的世界坐标系
- 确保路径点在同一个世界中

### 2. 速度单位
- 速度单位为米/秒（Minecraft中1格=1米）
- 建议速度范围：0.5-20米/秒

### 3. Tick精度
- 基于Minecraft的20TPS系统
- 1 tick = 1/20秒 = 50毫秒

### 4. 路径有效性
- 确保路径至少包含2个点
- 路径点之间的距离不应过小（建议>0.1格）

## 故障排除

### 常见问题

1. **位置计算返回null**
   - 检查路径是否为空
   - 确认是否已超出路径长度

2. **运动不平滑**
   - 尝试使用更高分辨率的平滑算法
   - 检查路径点密度是否合适

3. **性能问题**
   - 降低更新频率
   - 使用线性插值替代复杂算法
   - 减少路径点数量

4. **方向计算异常**
   - 确保路径点之间有足够距离
   - 检查是否在路径末端

通过这个实时位置计算系统，你可以实现精确、平滑的无人机运动控制，完全解决速度与预计算路径不匹配的问题。
