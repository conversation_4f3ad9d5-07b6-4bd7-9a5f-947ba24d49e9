package org.etwxr9.frostcraftbu.Cmd;

import java.util.ArrayList;
import java.util.List;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.etwxr9.buildingunit.BuildingUnitAPI;
import org.etwxr9.buildingunit.UnitInfo;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Listener.BuildSignListener;
import org.etwxr9.frostcraftbu.Manager.DroneManager;

public class CmdBuilding implements SubCmdBase {

    @Override
    public boolean onSubCommand(Player p, String[] args) {
        if (args.length == 0) {
            sendHelp(p);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "scan":
                return handleScan(p, args);
            case "delete":
                return handleDelete(p, args);
            case "manage":
                return handleManage(p, args);
            default:
                sendHelp(p);
                return true;
        }
    }

    private boolean handleManage(Player p, String[] args) {
        if (!p.hasPermission("frostcraftbu.command.building.manage")) {
            p.sendMessage("§c你没有权限使用此命令！");
            return true;
        }
        FCBuilding building = BuildingManager.i().getBuildingAt(p.getLocation());
        if (building == null) {
            p.sendMessage("§c你当前位置没有建筑！");
            return true;
        }
        // 打开管理菜单
        BuildSignListener.openManageUI(p, building, p.getLocation());
        return true;
    }

    private boolean handleScan(Player p, String[] args) {
        if (!p.hasPermission("frostcraftbu.command.building.scan")) {
            p.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        int radius = 10; // 默认半径
        if (args.length > 1) {
            try {
                radius = Integer.parseInt(args[1]);
                if (radius <= 0 || radius > 50) {
                    p.sendMessage("§c半径必须在1-50之间！");
                    return true;
                }
            } catch (NumberFormatException e) {
                p.sendMessage("§c半径必须是一个有效的数字！");
                return true;
            }
        }

        Location center = p.getLocation();
        var min = center.clone().subtract(radius, radius, radius);
        var max = center.clone().add(radius, radius, radius);
        List<UnitInfo> units = BuildingUnitAPI.getOverlapUnits(min, max);

        if (units.isEmpty()) {
            p.sendMessage("§e在半径 " + radius + " 格内没有找到任何建筑单元！");
            return true;
        }

        p.sendMessage("§a在半径 " + radius + " 格内找到 " + units.size() + " 个建筑单元：");

        for (UnitInfo unit : units) {
            String uuid = unit.getUuid();
            FCBuilding building = BuildingManager.i().getBuilding(uuid);

            if (building != null) {
                p.sendMessage("§a✓ §7Unit UUID: §f" + uuid +
                        " §7建筑ID: §f" + building.getBuildingId() +
                        " §7位置: §f" + formatLocation(unit.getOriginLocation()));
            } else {
                p.sendMessage("§c✗ §7Unit UUID: §f" + uuid +
                        " §7(无对应建筑) §7位置: §f" + formatLocation(unit.getOriginLocation()));
            }
        }

        return true;
    }

    private boolean handleDelete(Player p, String[] args) {
        if (!p.hasPermission("frostcraftbu.command.building.delete")) {
            p.sendMessage("§c你没有权限使用此命令！");
            return true;
        }
        UnitInfo unit = BuildingUnitAPI.getUnit(p.getLocation());
        if (unit == null) {
            p.sendMessage("§c你当前位置没有建筑单元！");
            return true;
        }
        String uuid = unit.getUuid();
        FCBuilding building = BuildingManager.i().getBuilding(uuid);
        if (building != null) {
            BuildingManager.i().removeBuilding(building, true);
            p.sendMessage("§a已成功删除当前位置的建筑！");
        } else {
            BuildingUnitAPI.deleteUnit(unit, (e) -> {
                // 筛出无人机及货物
                return DroneManager.i().isDrone(e) || DroneManager.i().isItem(e);
            });
            p.sendMessage("§a建筑不存在，已强制删除建筑单元 UUID: " + uuid);
        }

        return true;

    }

    private String formatLocation(Location loc) {
        return String.format("%.1f, %.1f, %.1f", loc.getX(), loc.getY(), loc.getZ());
    }

    private void sendHelp(Player p) {
        p.sendMessage("§e§l建筑管理命令帮助：");
        p.sendMessage("§7/fc building scan [半径] §f- 扫描周围建筑单元");
        p.sendMessage("§7/fc building delete §f- 删除当前位置的建筑或幽灵建筑单元");
    }

    @Override
    public List<String> onTabComplete(Player p, String[] args) {
        if (args.length == 1) {
            List<String> options = new ArrayList<>();
            if (p.hasPermission("frostcraftbu.command.building.scan"))
                options.add("scan");
            if (p.hasPermission("frostcraftbu.command.building.delete"))
                options.add("delete");
            if (p.hasPermission("frostcraftbu.command.building.manage")) {
                options.add("manage");
            }
            return options;
        } else if (args.length == 2) {
            if (args[0].equalsIgnoreCase("scan")) {
                return List.of("10", "20", "30");
            }
        }
        return null;
    }
}
