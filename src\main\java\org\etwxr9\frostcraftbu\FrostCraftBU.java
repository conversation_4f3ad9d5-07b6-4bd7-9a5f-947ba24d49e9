package org.etwxr9.frostcraftbu;

import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Cmd.CmdBuilding;
import org.etwxr9.frostcraftbu.Cmd.CmdGetSign;
import org.etwxr9.frostcraftbu.Cmd.CmdGetSlotFrame;
import org.etwxr9.frostcraftbu.Cmd.CmdManager;
import org.etwxr9.frostcraftbu.Cmd.CmdSample;
import org.etwxr9.frostcraftbu.Cmd.CmdTest;
import org.etwxr9.frostcraftbu.Cmd.CmdVote;
import org.etwxr9.frostcraftbu.Cmd.CmdDronePathTest;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Cmd.CmdRegisterItem;
import org.etwxr9.frostcraftbu.Cmd.CmdResearch;
import org.etwxr9.frostcraftbu.Listener.BuildSignListener;
import org.etwxr9.frostcraftbu.Listener.ConsumeSignListener;
import org.etwxr9.frostcraftbu.Listener.ContainerInteractListener;
import org.etwxr9.frostcraftbu.Listener.GatherSignListener;
import org.etwxr9.frostcraftbu.Listener.PlayerChatRangeListener;
import org.etwxr9.frostcraftbu.Listener.ProductionSignListener;
import org.etwxr9.frostcraftbu.Listener.SampleListener;
import org.etwxr9.frostcraftbu.Listener.SaveSignListener;
import org.etwxr9.frostcraftbu.Listener.SlotFramePlaceListener;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;
import org.etwxr9.frostcraftbu.Vote.VoteManager;
import org.ipvp.canvas.MenuFunctionListener;

import org.etwxr9.frostcraftbu.Manager.AuraManager;
import org.etwxr9.frostcraftbu.Manager.BuildingTickManager;
import org.etwxr9.frostcraftbu.Module.ModuleManager;
import org.etwxr9.frostcraftbu.Recipe.RecipeManager;
import org.etwxr9.frostcraftbu.pathfinding.PatheticPlugin;
import org.etwxr9.frostcraftbu.pathfinding.api.DronePathfindingService;

import de.bsommerfeld.pathetic.bukkit.PatheticBukkit;

public class FrostCraftBU extends JavaPlugin {

    private static FrostCraftBU instance;

    private static boolean loadCompleted = false;

    private FileConfiguration config;

    private DronePathfindingService dronePathfindingService;

    public static FrostCraftBU i() {
        return instance;
    }

    /**
     * 获取无人机寻路服务实例
     * 
     * @return DronePathfindingService实例，如果未初始化则返回null
     */
    public static DronePathfindingService getDronePathfindingService() {
        return instance != null ? instance.dronePathfindingService : null;
    }

    @Override
    public void onEnable() {
        instance = this;
        getLogger().info("Starting up frostcraftbu...");

        // read config
        saveDefaultConfig();
        saveResource("Building.yml", false);
        saveResource("TechTree.yml", false); // 确保TechTree.yml存在
        config = getConfig();
        ItemManager.loadItems();
        TechTreeManager.i().loadConfig();

        // register module
        ModuleManager.registerModule();
        // 加载科技树和建筑（需要在读取配置之后）
        TechTreeManager.i().loadResearchData();
        BuildingManager.i().loadBuilding();

        // 初始化 BuildingManager (确保在 TickManager 之前或同时)
        // BuildingManager.init(); // init 通常是静态块或构造函数做的事，确认是否需要调用
        // BuildingManager.i().loadBuilding(); // 加载建筑数据，这会调用模块的 onLoad - 重复加载？检查逻辑

        // register command
        getCommand("frostcraftbu").setExecutor(new CmdManager());
        // register subcommand
        CmdManager.registerSubCommand("sample", new CmdSample());
        CmdManager.registerSubCommand("test", new CmdTest());
        CmdManager.registerSubCommand("getsign", new CmdGetSign());
        CmdManager.registerSubCommand("vote", new CmdVote());
        CmdManager.registerSubCommand("registeritem", new CmdRegisterItem());
        CmdManager.registerSubCommand("research", new CmdResearch());
        CmdManager.registerSubCommand("getslotframe", new CmdGetSlotFrame());
        CmdManager.registerSubCommand("building", new CmdBuilding());
        CmdManager.registerSubCommand("vote", new CmdVote());
        CmdManager.registerSubCommand("dronetest", new CmdDronePathTest());

        // register listener
        Bukkit.getPluginManager().registerEvents(new MenuFunctionListener(), this);
        Bukkit.getPluginManager().registerEvents(new SampleListener(), this);
        Bukkit.getPluginManager().registerEvents(new BuildSignListener(), this);
        Bukkit.getPluginManager().registerEvents(new SaveSignListener(), this);
        Bukkit.getPluginManager().registerEvents(new ContainerInteractListener(), this);
        Bukkit.getPluginManager().registerEvents(new SlotFramePlaceListener(), this);
        Bukkit.getPluginManager().registerEvents(new ProductionSignListener(), this);
        Bukkit.getPluginManager().registerEvents(new ConsumeSignListener(), this);
        Bukkit.getPluginManager().registerEvents(new GatherSignListener(), this);
        Bukkit.getPluginManager().registerEvents(new PlayerChatRangeListener(), this);

        // 初始化和注册投票系统
        VoteManager.init(); // 确保这个方法不会依赖尚未加载的东西

        // 启动 AuraManager
        AuraManager.getInstance().start(this); // 传入插件实例

        // 加载配方
        RecipeManager.i().loadRecipes();

        // 启动生产管理器
        BuildingTickManager.i().start();

        // 初始化无人机寻路服务
        PatheticBukkit.initialize(this);
        dronePathfindingService = new DronePathfindingService(this);
        new PatheticPlugin().enable(this);
        getLogger().info("无人机寻路服务已初始化");

        // 添加一个现实时间记分板，每秒将记分设定为当天的秒数（防止服务器不同步，每次要计算现实时间并赋值）
        new BukkitRunnable() {
            @Override
            public void run() {
                Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                Objective objective = scoreboard.getObjective(config.getString("ScoreboardObjectives.RealTimeObj"));
                if (objective != null) {
                    long currentTime = System.currentTimeMillis();
                    long midnight = currentTime - (currentTime % 86400000); // 一天的毫秒数
                    objective.getScore(config.getString("ScoreboardObjectives.RealTimeEntry"))
                            .setScore((int) ((currentTime - midnight) / 1000));
                }
            }
        }.runTaskTimer(this, 0L, 20L);

        getLogger().info("frostcraftbu has been enabled!"); // 提示加载完成
        loadCompleted = true;
    }

    @Override
    public void onDisable() {
        getLogger().info("Shutting down frostcraftbu...");
        if (!loadCompleted) {
            getLogger().info("load failed, frostcraftbu has been disabled!");
            return;
        }

        // 停止 AuraManager
        AuraManager.getInstance().stop();

        // 保存建筑数据
        if (BuildingManager.i() != null) {
            getLogger().info("Saving buildings...");
            BuildingManager.i().saveBuilding();
            getLogger().info("Buildings saved.");
        } else {
            getLogger().warning("BuildingManager was null during disable.");
        }
        // 保存科技
        TechTreeManager.i().saveResearchData();

        Bukkit.getScheduler().cancelTasks(this);
        getLogger().info("frostcraftbu has been disabled!");
    }
}
