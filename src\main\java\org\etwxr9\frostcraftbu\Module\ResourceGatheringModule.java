package org.etwxr9.frostcraftbu.Module;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Module.Behavior.IResourceGathering;
import org.etwxr9.frostcraftbu.Recipe.GatherRecipeConfig;
import org.etwxr9.frostcraftbu.Recipe.RecipeManager;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class ResourceGatheringModule extends BaseModule implements IResourceGathering {

    // 配置数据类
    private class ProductionLineConfig {
        List<Integer> outputSlots;
        List<String> availableRecipes;
    }

    // 运行时状态类
    private class ProductionLineState {
        String currentRecipe;
        int progress;
    }

    // 产线类，组合配置和状态
    private class ProductionLine {
        ProductionLineConfig config;
        ProductionLineState state;

        ProductionLine(ProductionLineConfig config) {
            this.config = config;
            this.state = new ProductionLineState();
            this.state.currentRecipe = null;
            this.state.progress = 0;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResourceGatheringModuleSaveData extends BaseModuleSaveData {
        private static class ProductionLineStateSaveData {
            private String currentRecipe;
            private int progress;

            public ProductionLineStateSaveData() {
            }

            public ProductionLineStateSaveData(ProductionLineState state) {
                this.currentRecipe = state.currentRecipe;
                this.progress = state.progress;
            }
        }

        private List<ProductionLineStateSaveData> productionLineStates;

        public ResourceGatheringModuleSaveData() {
        }

        public ResourceGatheringModuleSaveData(List<ProductionLineStateSaveData> states) {
            this.productionLineStates = states;
        }

        public List<ProductionLineStateSaveData> getProductionLineStates() {
            return productionLineStates;
        }

        @Override
        public String getModuleTypeId() {
            return ModuleManager.ModuleType.ResourceGatheringModule.getName();
        }
    }

    private List<ProductionLine> productionLines = new ArrayList<>();
    private ContainerModule containerModule;

    public ResourceGatheringModule(FCBuilding building) {
        super(building);
    }

    @Override
    public void onLoad(boolean first) {
        containerModule = (ContainerModule) building.getModule("container");
        if (containerModule == null) {
            throw new IllegalStateException("ResourceGatheringModule requires ContainerModule");
        }

        // 从配置加载产线配置
        List<Map<?, ?>> lines = getModuleConfig().getMapList("lines");
        for (Map<?, ?> line : lines) {
            ProductionLineConfig config = new ProductionLineConfig();
            config.outputSlots = (List<Integer>) line.get("output");
            config.availableRecipes = (List<String>) line.get("recipe");

            productionLines.add(new ProductionLine(config));
        }
    }

    @Override
    public void tick() {
        for (int i = 0; i < productionLines.size(); i++) {
            ProductionLine line = productionLines.get(i);
            if (line.state.currentRecipe == null)
                continue;

            if (canProduce(i)) {
                GatherRecipeConfig recipe = RecipeManager.i().getGatherRecipe(line.state.currentRecipe);
                line.state.progress++;

                if (line.state.progress >= recipe.getTime()) {
                    produce(i);
                    line.state.progress = 0;
                }
            }
        }
    }

    private void produce(int lineIndex) {
        ProductionLine line = productionLines.get(lineIndex);
        GatherRecipeConfig recipe = RecipeManager.i().getGatherRecipe(line.state.currentRecipe);

        // 添加输出物品
        for (Map.Entry<String, Integer> output : recipe.getOutput().entrySet()) {
            int remaining = output.getValue();
            for (int slotId : line.config.outputSlots) {
                if (remaining <= 0)
                    break;
                String slotItemId = containerModule.getSlotItemId(slotId);
                int slotMaxCount = containerModule.getSlotMaxCount(slotId);

                if (slotItemId == null || output.getKey().equals(slotItemId)) {
                    int slotCount = containerModule.getSlotItemCount(slotId);
                    int space = slotMaxCount - slotCount;
                    int add = Math.min(remaining, space);
                    containerModule.setSlotItem(slotId, output.getKey(), slotCount + add);
                    remaining -= add;
                }
                // 消耗蒸汽
                var comsumeSteamobj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamObj");
                var comsumeSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamEntry");
                var comsumeSteamValue = recipe.getSteam();
                if (comsumeSteamobj != null && comsumeSteamEntry != null) {
                    try {
                        Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                        Objective objective = scoreboard.getObjective(comsumeSteamobj);
                        if (objective != null) {
                            Score score = objective.getScore(comsumeSteamEntry);
                            if (score.getScore() < comsumeSteamValue) {
                                return;
                            }
                            score.setScore(score.getScore() - comsumeSteamValue);
                        } else {
                            // 重要提示: 只有在配置了对应名称的记分项时才会生效
                            // 可以考虑在插件启动时检查或创建这些记分项
                            FrostCraftBU.i().getLogger().warning("记分板目标 '" + comsumeSteamobj + "' 不存在！请在游戏中创建它。");
                        }
                    } catch (Exception e) {
                        FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + comsumeSteamobj + "): ", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean setRecipe(int lineIndex, String recipeId) {
        if (lineIndex >= productionLines.size())
            return false;

        ProductionLine line = productionLines.get(lineIndex);
        if (!line.config.availableRecipes.contains(recipeId))
            return false;

        line.state.currentRecipe = recipeId;
        line.state.progress = 0;

        // 更新容器slot的物品类型
        if (recipeId != null) {
            GatherRecipeConfig recipe = RecipeManager.i().getGatherRecipe(recipeId);
            // 设置输出槽
            for (int i = 0; i < line.config.outputSlots.size(); i++) {
                int slotId = line.config.outputSlots.get(i);
                if (i < recipe.getOutput().size()) {
                    String itemId = new ArrayList<>(recipe.getOutput().keySet()).get(i);
                    containerModule.setSlotItem(slotId, itemId, 0);
                }
            }
        } else {
            for (int slotId : line.config.outputSlots) {
                containerModule.clearSlot(slotId);
            }
        }
        return true;
    }

    @Override
    public String getRecipe(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return null;
        return productionLines.get(lineIndex).state.currentRecipe;
    }

    @Override
    public int getProgress(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return 0;
        return productionLines.get(lineIndex).state.progress;
    }

    @Override
    public boolean canProduce(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return false;

        ProductionLine line = productionLines.get(lineIndex);
        if (line.state.currentRecipe == null)
            return false;

        GatherRecipeConfig recipe = RecipeManager.i().getGatherRecipe(line.state.currentRecipe);

        if (recipe == null) {
            return false;
        }
        //log
        // FrostCraftBU.i().getLogger().info("canProduce: " + recipe.getOutput().toString());

        // 检查输出槽是否有足够空间
        for (Map.Entry<String, Integer> output : recipe.getOutput().entrySet()) {
            int remainingOutput = output.getValue();
            for (int slotId : line.config.outputSlots) {
                String slotItemId = containerModule.getSlotItemId(slotId);
                if (slotItemId == null || output.getKey().equals(slotItemId)) {
                    int space = containerModule.getSlotMaxCount(slotId) - containerModule.getSlotItemCount(slotId);
                    remainingOutput -= space;
                    if (remainingOutput <= 0)
                        break;
                }
            }
            if (remainingOutput > 0)
                return false;
        }

        // 检查蒸汽是否足够
        var comsumeSteamobj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamObj");
        var comsumeSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamEntry");
        var comsumeSteamValue = recipe.getSteam();
        if (comsumeSteamobj != null && comsumeSteamEntry != null) {
            try {
                Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                Objective objective = scoreboard.getObjective(comsumeSteamobj);
                if (objective != null) {
                    Score score = objective.getScore(comsumeSteamEntry);
                    if (score.getScore() < comsumeSteamValue) {
                        return false;
                    }
                } else {
                    FrostCraftBU.i().getLogger().warning("记分板目标 '" + comsumeSteamobj + "' 不存在！请在游戏中创建它。");
                }
            } catch (Exception e) {
                FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + comsumeSteamobj + "): ", e);
            }
        }

        return true;
    }

    @Override
    public BaseModuleSaveData getSaveData() {
        List<ResourceGatheringModuleSaveData.ProductionLineStateSaveData> saveStates = new ArrayList<>();
        for (ProductionLine line : productionLines) {
            saveStates.add(new ResourceGatheringModuleSaveData.ProductionLineStateSaveData(line.state));
        }
        return new ResourceGatheringModuleSaveData(saveStates);
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ResourceGatheringModuleSaveData) {
            ResourceGatheringModuleSaveData saveData = (ResourceGatheringModuleSaveData) data;
            List<ResourceGatheringModuleSaveData.ProductionLineStateSaveData> states = saveData.getProductionLineStates();

            // 确保状态数量与产线数量匹配
            for (int i = 0; i < Math.min(states.size(), productionLines.size()); i++) {
                ResourceGatheringModuleSaveData.ProductionLineStateSaveData stateSaveData = states.get(i);
                ProductionLine line = productionLines.get(i);
                line.state.currentRecipe = stateSaveData.currentRecipe;
                line.state.progress = stateSaveData.progress;
            }
        }
    }

    @Override
    public void onUnload() {
    }

    @Override
    public String getModuleTypeId() {
        return ModuleManager.ModuleType.ResourceGatheringModule.getName();
    }

    @Override
    public List<String> getAvailableRecipes(int lineIndex) {
        var arBuilding = productionLines.get(lineIndex).config.availableRecipes;
        var arTech = TechTreeManager.i().getUnlockedGatherRecipes();
        arBuilding.retainAll(arTech);
        return arBuilding;
    }

    @Override
    public int getLineCount() {
        return productionLines.size();
    }

}