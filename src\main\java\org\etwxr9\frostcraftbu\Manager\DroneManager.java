package org.etwxr9.frostcraftbu.Manager;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import javax.annotation.Nullable;

import org.bukkit.Location;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Entity;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scoreboard.Scoreboard;
import org.bukkit.util.Vector;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Module.BaseModule;
import org.etwxr9.frostcraftbu.Module.ContainerModule;
import org.etwxr9.frostcraftbu.pathfinding.api.PathSmoothingUtil;
import org.etwxr9.frostcraftbu.pathfinding.api.DronePathResult;

/**
 * 无人机管理器
 * 
 * 静态值：
 * 平滑类型 SmoothingType CATMULL_ROM
 * 分辨率 Resolution 5
 * 
 * droneMap: UUID:entity
 * 
 * 配置:
 * 路径平滑参数（也是无人机速度乘数）PathSmooth
 * 无人机参数记分板Obj DroneObj
 * 无人机速度Entry(米/秒) DroneSpeedEntry
 * 无人机容量上限Entry DroneCapacityEntry
 * 无人机蒸汽上限Entry DroneSteamEntry
 * 无人机消耗蒸汽速度Entry DroneConsumeSteamEntry
 * 装卸货时间（tick） LoadUnloadTime
 * 充电时间（tick） ChargeTime
 * 货物偏移向量 DroneItemOffset
 * 
 * 
 * 无人机物品PDC储存
 * 起始建筑uuid startBuildingUUID
 * 目标建筑uuid endBuildingUUID
 * 
 * 无人机实体PDC储存
 * 起始建筑uuid startBuildingUUID
 * 目标建筑uuid endBuildingUUID
 * 起始展示框index startFrameIndex
 * 目的展示框index endFrameIndex
 * 特殊路径起点 specialPathStart
 * 特殊路径终点 specialPathEnd
 * 运行阶段 runningStage(-1-初始化到起始建筑 0-起始展示框到起始建筑 1-起始建筑到目标建筑 2-目标建筑到目标展示框
 * 3-目标展示框到目标建筑
 * 4-目标建筑到起始建筑 5-起始建筑到起始展示框)
 * 充电阶段 chargeStage(0-不需充电 1-前往充电桩建筑 2-从建筑到充电桩 3-从充电桩返回建筑)
 * 状态 runningStatus(0-空闲 1-常规运行中 2-装卸货 3-充电中 4-找不到路径 5-路径阻塞)
 * 路径index pathIndex
 * 蒸汽消耗计数 steamConsumeCount
 * 货物实体UUID itemUUID
 * 
 * 货物display PDC储存
 * 货物itemID itemID
 * 货物数量 count
 * 
 * tick()执行无人机逻辑：
 * 1. 检查是否处于空闲、装卸货、充电中、找不到路径、路径阻塞状态，并执行对应逻辑。装卸货、充电完成时，重新设定充电阶段及运行阶段，返回
 * 2. 检查是否处于非0的充电阶段，如果是则执行对应逻辑，返回
 * 3. 检查是否耗尽蒸汽，如果耗尽，则进入充电阶段1，遍历建筑，寻找最近的充电桩，设定特殊路径起终点，返回
 * 4. 检查目标建筑是否存在，如果不存在进入找不到路径状态，公屏播报，返回
 * 5. 检查目标路径是否存在，如果不存在则检查genPathMap，没有则开始生成路径，路径不通则进入找不到路径状态，公屏播报，返回
 * 6. 检查是否到达路径终点，如果到达则进入下一阶段，检查当前完成的路径是否是临时路径并删除。全部结束则启动装卸货、充电中逻辑，返回
 * 7. 如果下一个路径点是实体方块则进入路径阻塞状态，删除当前路径，公屏播报
 * 8. 向下一个路径点移动，并增加蒸汽消耗计数，如果计数达到一分钟则消耗蒸汽，返回
 * 
 * 
 * 具体的充电逻辑：
 * 
 * 当进入充电阶段1时，根据特殊路径起终点确定路径并移动到下一个路径点。
 * 到达目的地时，检索建筑充电桩，进入充电阶段2，重新设定特殊路径。
 * 到达目的地时，确认建筑充电桩，进入充电阶段3，进入充电中状态
 * 如果运行阶段处于0、1、2、3阶段，则充电完成后设定运行阶段为1，前往目标建筑处，
 * 如果处于4、5阶段，则设定运行阶段为4，充电完成后前往起始建筑处。
 * 
 * 装卸货逻辑：当无人机完成4运行阶段到达起始建筑时，检查起始建筑和目标建筑的container模块的output、input展示框，
 * 根据物品类型和数量，匹配可运输的两个展示框，并设定无人机的起终展示框index，接着进入运行阶段5
 * 如果没有可运输物品，进入状态0（空闲）。
 * 完成运行阶段5时，此时无人机到达起始展示框，将展示框中的物品放入无人机（即修改数量，生成物品display）进入运行阶段0
 * 完成运行阶段2时，此时无人机到达目标展示框，卸载无人机中的物品（即修改数量，删除物品display）进入运行阶段3
 * 
 * 
 * pathMap: pathKey:DronePathResult
 * pathKey: 一个有序复合键类但支持无序匹配，储存两个位置，重写equal保证无序匹配。
 * 临时路径flag，用于在无人机完成路径后判断是否删除。
 * 正逆判断，判断给定起始点是正向还是逆向
 * 
 * addPath、removePath 向pathMap中加入或移除路径
 * 
 * generatingPathMap: 一个记录当前正在生成的路径的map，key为pathKey，value为路径生成的CompletableFuture
 * 
 * genPath: 异步函数，生成路径，开始执行前，加入generatingPathMap，完成后移除并加入pathMap。
 * 如果传入两个建筑uuid，则生成之间的路径。如果传入一个建筑uuid，则生成从建筑展示框、充电桩到建筑门口的路径。
 * 
 * removeBuildingPath: 根据建筑的位置删除所有一端为该位置的路径(这就同时包括了建筑到建筑和展示框到建筑)
 * 
 * initDrone: 初始化无人机实体，传入参数：起终点建筑uuid。在当前建筑的位置生成无人机实体，设定PDC，加入无人机列表，进入运行阶段4。
 * 
 * getDroneItemUI: 返回无人机物品UI，展示：操作说明、设定起始建筑按钮、设定目标建筑按钮、部署按钮
 * 
 * getDroneUI: 返回无人机实体UI，展示：起始建筑、目标建筑、承载物品（可拿走）、剩余蒸汽、拿起无人机
 * 
 * removeDrone: 玩家拿起无人机时调用，删除无人机实体，从无人机列表中移除，生成无人机物品，掉落在原地，如果无人机有货物，掉落在原地
 */
public class DroneManager {

    // 单例
    private static DroneManager i;

    public static synchronized DroneManager i() {
        if (i == null) {
            i = new DroneManager();
        }
        return i;
    }

    private final static PathSmoothingUtil.SmoothingType SmoothingType = PathSmoothingUtil.SmoothingType.CATMULL_ROM;
    private final static int Resolution = 5;

    // all PDC namespacekey
    private final NamespacedKey startBuildingUUIDKey = new NamespacedKey(FrostCraftBU.i(), "startBuildingUUID");
    private final NamespacedKey endBuildingUUIDKey = new NamespacedKey(FrostCraftBU.i(), "endBuildingUUID");
    private final NamespacedKey startFrameIndexKey = new NamespacedKey(FrostCraftBU.i(), "startFrameIndex");
    private final NamespacedKey endFrameIndexKey = new NamespacedKey(FrostCraftBU.i(), "endFrameIndex");
    private final NamespacedKey specialPathStartKey = new NamespacedKey(FrostCraftBU.i(), "specialPathStart");
    private final NamespacedKey specialPathEndKey = new NamespacedKey(FrostCraftBU.i(), "specialPathEnd");
    private final NamespacedKey runningStageKey = new NamespacedKey(FrostCraftBU.i(), "runningStage");
    private final NamespacedKey chargeStageKey = new NamespacedKey(FrostCraftBU.i(), "chargeStage");
    private final NamespacedKey runningStatusKey = new NamespacedKey(FrostCraftBU.i(), "runningStatus");
    private final NamespacedKey pathIndexKey = new NamespacedKey(FrostCraftBU.i(), "pathIndex");
    private final NamespacedKey steamConsumeCountKey = new NamespacedKey(FrostCraftBU.i(), "steamConsumeCount");
    private final NamespacedKey itemUUIDKey = new NamespacedKey(FrostCraftBU.i(), "itemUUID");
    private final NamespacedKey itemIDKey = new NamespacedKey(FrostCraftBU.i(), "itemID");
    private final NamespacedKey itemCountKey = new NamespacedKey(FrostCraftBU.i(), "itemCount");

    private final HashMap<String, Entity> droneMap = new HashMap<>();
    private final HashMap<PathKey, DronePathResult> pathMap = new HashMap<>();

    public DroneManager() {
    }

    @Nullable
    private String getNearestChargingBuilding(Location loc) {
        double nearestCBDistance = Double.MAX_VALUE;
        String nearestCBUUID = null;
        for (FCBuilding b : BuildingManager.i().getAllBuildings()) {
            if (b.getBuildingConfig().getModuleConfig("charging") != null) {
                if (b.getCenter().distance(loc) < nearestCBDistance) {
                    nearestCBDistance = b.getCenter().distance(loc);
                    nearestCBUUID = b.getUnitUUID();
                }
            }
        }
        return nearestCBUUID;
    }

    // 判断实体是否是无人机
    public boolean isDrone(Entity entity) {
        return droneMap.containsKey(entity.getUniqueId().toString());
    }

    // 判断实体是否是货物
    public boolean isItem(Entity entity) {
        return entity.getPersistentDataContainer().has(itemUUIDKey, PersistentDataType.STRING);
    }

    /**
     * 匹配无人机起始建筑和目标建筑的可运输展示框
     * 
     * @param droneUUID 无人机UUID
     * @return Optional<int[]> 包含起始展示框index和目标展示框index的数组
     */
    private Optional<int[]> matchSlotIndex(String droneUUID) {
        // 检查是否有无人机
        if (!droneMap.containsKey(droneUUID)) {
            return Optional.empty();
        }
        Entity drone = droneMap.get(droneUUID);
        if (drone == null) {
            return Optional.empty();
        }
        // 检查是否有起终点建筑UUID
        PersistentDataContainer pdc = drone.getPersistentDataContainer();
        if (!pdc.has(startBuildingUUIDKey, PersistentDataType.STRING)
                || !pdc.has(endBuildingUUIDKey, PersistentDataType.STRING)) {
            return Optional.empty();
        }
        String startBuildingUUID = pdc.get(startBuildingUUIDKey, PersistentDataType.STRING);
        String endBuildingUUID = pdc.get(endBuildingUUIDKey, PersistentDataType.STRING);
        if (startBuildingUUID == null || endBuildingUUID == null) {
            return Optional.empty();
        }
        // 检查是否有起终点建筑UUID以及容器模块
        FCBuilding startBuilding = BuildingManager.i().getBuilding(startBuildingUUID);
        FCBuilding endBuilding = BuildingManager.i().getBuilding(endBuildingUUID);
        if (startBuilding == null || endBuilding == null) {
            return Optional.empty();
        }
        BaseModule startContainer = startBuilding.getModule("container");
        BaseModule endContainer = endBuilding.getModule("container");
        if (startContainer == null || endContainer == null) {
            return Optional.empty();
        }
        ContainerModule sc = (ContainerModule) startContainer;
        ContainerModule ec = (ContainerModule) endContainer;
        // 检查是否有output、input容器
        List<Integer> startSlots = sc.getSlotsByTag("output");
        List<Integer> endSlots = ec.getSlotsByTag("input");
        if (startSlots.isEmpty() || endSlots.isEmpty()) {
            return Optional.empty();
        }
        // 检查是否有可运输的物品，并返回index
        for (int ss : startSlots) {
            String startItemId = sc.getSlotItemId(ss);
            int startItemCount = sc.getSlotItemCount(ss);
            if (startItemId == null || startItemCount <= 0) {
                continue;
            }
            for (int es : endSlots) {
                String endItemId = ec.getSlotItemId(es);
                int endItemCount = ec.getSlotItemCount(es);
                if (endItemId == null || endItemCount >= ec.getSlotMaxCount(es)) {
                    continue;
                }
                if (startItemId.equals(endItemId)) {
                    return Optional.of(new int[] { ss, es });
                }
            }
        }
        return Optional.empty();
    }

    // scoreboard
    private double getDroneSpeed() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneSpeedEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneSpeedEntry");
        if (droneSpeedEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 1;
        }
        return scoreboard.getObjective(droneObj).getScore(droneSpeedEntry).getScore();
    }

    private int getDroneCapacity() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneCapacityEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneCapacityEntry");
        if (droneCapacityEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 1;
        }
        return scoreboard.getObjective(droneObj).getScore(droneCapacityEntry).getScore();
    }

    private int getDroneSteam() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneSteamEntry");
        if (droneSteamEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 1;
        }
        return scoreboard.getObjective(droneObj).getScore(droneSteamEntry).getScore();
    }

    private int getDroneConsumeSteam() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneConsumeSteamEntry = FrostCraftBU.i().getConfig()
                .getString("ScoreboardObjectives.DroneConsumeSteamEntry");
        if (droneConsumeSteamEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 1;
        }
        return scoreboard.getObjective(droneObj).getScore(droneConsumeSteamEntry).getScore();
    }

    // config
    private int getLoadUnloadTime() {
        return FrostCraftBU.i().getConfig().getInt("Drone.LoadUnloadTime", 20);
    }

    private int getChargeTime() {
        return FrostCraftBU.i().getConfig().getInt("Drone.ChargeTime", 20);
    }

    private Vector getDroneItemOffset() {
        return FrostCraftBU.i().getConfig().getVector("Drone.DroneItemOffset", new Vector(0, 0, 0));
    }

    public class PathKey {
        private Location start;
        private Location end;
        private boolean isTemp;

        public PathKey(Location start, Location end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            PathKey pathKey = (PathKey) o;
            return (start.equals(pathKey.start) && end.equals(pathKey.end)) ||
                    (start.equals(pathKey.end) && end.equals(pathKey.start));
        }

        @Override
        public int hashCode() {
            return start.hashCode() + end.hashCode();
        }

        public boolean isReverse(PathKey other) {
            return (start.equals(other.end) && end.equals(other.start));
        }

        public boolean isTemp() {
            return isTemp;
        }

        public void setTemp(boolean temp) {
            isTemp = temp;
        }

    }

}
