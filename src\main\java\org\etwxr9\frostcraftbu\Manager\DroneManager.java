package org.etwxr9.frostcraftbu.Manager;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import javax.annotation.Nullable;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Interaction;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scoreboard.Scoreboard;
import org.bukkit.util.Vector;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.BaseModule;
import org.etwxr9.frostcraftbu.Module.ContainerModule;
import org.etwxr9.frostcraftbu.Module.ModuleManager;
import org.etwxr9.frostcraftbu.pathfinding.api.DronePathResult;
import org.etwxr9.frostcraftbu.pathfinding.api.DronePathfindingService;
import org.etwxr9.frostcraftbu.pathfinding.api.PathSmoothingUtil;
import org.ipvp.canvas.type.ChestMenu;

import com.fasterxml.jackson.databind.ObjectMapper;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.TextComponent;
import net.kyori.adventure.text.format.NamedTextColor;

import org.etwxr9.frostcraftbu.pathfinding.api.DronePathResult;

public class DroneManager {

    // 单例
    private static DroneManager i;

    public static synchronized DroneManager i() {
        if (i == null) {
            i = new DroneManager();
        }
        return i;
    }

    private final static PathSmoothingUtil.SmoothingType SmoothingType = PathSmoothingUtil.SmoothingType.CATMULL_ROM;
    private final static int Resolution = 5;

    // all PDC namespacekey
    // interactionPDC
    private final NamespacedKey interactEntityUUIDKey = new NamespacedKey(FrostCraftBU.i(), "interactEntityUUID");
    private final NamespacedKey droneUUIDKey = new NamespacedKey(FrostCraftBU.i(), "droneUUID");
    // dronePDC
    private final NamespacedKey startBuildingUUIDKey = new NamespacedKey(FrostCraftBU.i(), "startBuildingUUID");
    private final NamespacedKey endBuildingUUIDKey = new NamespacedKey(FrostCraftBU.i(), "endBuildingUUID");
    private final NamespacedKey chargeBuildingUUIDKey = new NamespacedKey(FrostCraftBU.i(), "chargeBuildingUUID");
    private final NamespacedKey startFrameIndexKey = new NamespacedKey(FrostCraftBU.i(), "startFrameIndex");
    private final NamespacedKey endFrameIndexKey = new NamespacedKey(FrostCraftBU.i(), "endFrameIndex");
    private final NamespacedKey pathStartKey = new NamespacedKey(FrostCraftBU.i(), "pathStart");
    private final NamespacedKey pathEndKey = new NamespacedKey(FrostCraftBU.i(), "pathEnd");
    private final NamespacedKey runningStageKey = new NamespacedKey(FrostCraftBU.i(), "runningStage");
    private final NamespacedKey chargeStageKey = new NamespacedKey(FrostCraftBU.i(), "chargeStage");
    private final NamespacedKey statusKey = new NamespacedKey(FrostCraftBU.i(), "runningStatus");
    private final NamespacedKey pathIndexKey = new NamespacedKey(FrostCraftBU.i(), "pathIndex");
    private final NamespacedKey steamConsumeCountKey = new NamespacedKey(FrostCraftBU.i(), "steamConsumeCount");
    private final NamespacedKey steamKey = new NamespacedKey(FrostCraftBU.i(), "steam");
    private final NamespacedKey cargoUUIDKey = new NamespacedKey(FrostCraftBU.i(), "cargoUUID");
    private final NamespacedKey cargoIDKey = new NamespacedKey(FrostCraftBU.i(), "cargoID");
    private final NamespacedKey cargoCountKey = new NamespacedKey(FrostCraftBU.i(), "cargoCount");

    private final Map<String, DroneData> droneDataMap = new HashMap<>();
    private final Map<String, ItemDisplay> droneCacheMap = new HashMap<>();
    private final HashMap<PathKey, DronePathResult> pathMap = new HashMap<>();
    private final HashMap<PathKey, CompletableFuture<DronePathResult>> generatingPathMap = new HashMap<>();

    private final HashMap<String, Integer> droneLoadUnloadTimeMap = new HashMap<>();
    private final HashMap<String, Integer> droneChargeTimeMap = new HashMap<>();
    private final HashMap<String, Integer> droneErrorMsgTimeMap = new HashMap<>();

    private void loadDroneData() {
        var droneJsonPath = Path.of(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/DroneSaveData.json");
        if (!Files.exists(droneJsonPath)) {
            FrostCraftBU.i().getLogger().info("无人机数据文件不存在，将创建新的无人机数据");
            return;
        }
        try {
            String jsonContent = Files.readString(droneJsonPath);
            ObjectMapper mapper = new ObjectMapper();
            var saveDataMap = (HashMap<String, DroneData>) mapper.readValue(jsonContent,
                    mapper.getTypeFactory().constructMapType(HashMap.class, String.class, DroneData.class));
            saveDataMap.forEach((uuid, data) -> {
                droneDataMap.put(uuid, data);
            });
        } catch (IOException e) {
            FrostCraftBU.i().getLogger().severe("加载无人机数据时出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private void saveDroneData() {
        var droneJsonPath = Path.of(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/DroneSaveData.json");
        try {
            ObjectMapper mapper = new ObjectMapper();
            String result = mapper.writeValueAsString(droneDataMap);
            Files.writeString(droneJsonPath, result);
        } catch (IOException e) {
            FrostCraftBU.i().getLogger().severe("保存无人机数据时出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateDroneData(String uuid, double x, double y, double z) {
        if (!droneDataMap.containsKey(uuid)) {
            return;
        }
        var data = droneDataMap.get(uuid);
        data.setX(x);
        data.setY(y);
        data.setZ(z);
    }

    public DroneManager() {
        loadDroneData(); // 加载无人机数据
        // 每分钟保存
        new BukkitRunnable() {
            @Override
            public void run() {
                saveDroneData();
            }
        }.runTaskTimer(FrostCraftBU.i(), 0L, 20L * 60L);
        // tick
        new BukkitRunnable() {
            @Override
            public void run() {
                droneDataMap.forEach((uuid, data) -> {
                    tickDrone(uuid);
                });
            }
        }.runTaskTimer(FrostCraftBU.i(), 0L, 1L);
    }

    // 获取一个建筑的朝向的边的中点位置
    private Location getBuildingEdgeCenter(FCBuilding building) {
        var r = building.getRotate();
        var offsetVec = new Vector(0, 0, 0);
        var min = building.getMinLocation();
        var max = building.getMaxLocation();
        switch (r) {
            case 0:
                offsetVec = new Vector(0, 0, (max.getZ() - min.getZ()) / 2);
                break;
            case 1:
                offsetVec = new Vector((max.getX() - min.getX()) / 2, 0, 0);
                break;
            case 2:
                offsetVec = new Vector(0, 0, -(max.getZ() - min.getZ()) / 2);
                break;
            case 3:
                offsetVec = new Vector(-(max.getX() - min.getX()) / 2, 0, 0);
                break;
        }
        var result = building.getCenter().add(offsetVec);
        result.setY(min.getY());
        return result;
    }

    // 获取一个建筑指定index的展示框的无人机停靠位置（展示框朝向+1，Y轴-1）
    private Optional<Location> getFrameLocation(FCBuilding building, int index) {
        // 检查容器模块
        if (building.getModule("container") == null) {
            return Optional.empty();
        }
        ContainerModule containerModule = (ContainerModule) building.getModule("container");
        // 检查展示框是否存在
        var frameLoc = containerModule.getItemFrameLocation(index);
        if (frameLoc == null) {
            return Optional.empty();
        }
        var loc = frameLoc.clone();
        loc.add(frameLoc.getDirection().multiply(1.5));
        loc.subtract(new Vector(0, 1, 0));
        return Optional.of(loc);
    }

    // 获取最近的充电桩建筑
    private Optional<String> getNearestChargeBuilding(Location loc) {
        double nearestCBDistance = Double.MAX_VALUE;
        Optional<String> nearestCBUUID = Optional.empty();
        for (FCBuilding b : BuildingManager.i().getAllBuildings()) {
            if (b.getBuildingConfig().getModuleConfig("charging") != null) {
                if (b.getCenter().distance(loc) < nearestCBDistance) {
                    nearestCBDistance = b.getCenter().distance(loc);
                    nearestCBUUID = Optional.of(b.getUnitUUID());
                }
            }
        }
        return nearestCBUUID;
    }

    private Optional<Location> getChargeLocation(FCBuilding chargeBuilding) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getChargingLocation'");
    }

    // 从UUID获取无人机
    public Optional<ItemDisplay> getDrone(String uuid) {
        if (!droneDataMap.containsKey(uuid)) {
            return Optional.empty();
        }
        if (droneCacheMap.containsKey(uuid)) {
            return Optional.of(droneCacheMap.get(uuid));
        } else {
            // 加载区块
            var data = droneDataMap.get(uuid);
            var world = Bukkit.getWorld(data.getWorldName());
            if (world == null) {
                return Optional.empty();
            }
            var loc = new Location(world, data.getX(), data.getY(), data.getZ());
            loc.getChunk().load();
            // 尝试获取UUID无人机实体
            var drone = world.getEntity(UUID.fromString(uuid));
            // 如果没有，删除数据
            if (drone == null) {
                droneDataMap.remove(uuid);
                saveDroneData();
                return null;
            } else {
                droneCacheMap.put(uuid, (ItemDisplay) drone);
                return Optional.of((ItemDisplay) drone);
            }
        }
    }

    public Optional<ItemDisplay> getCargo(String droneUUID) {
        var drone = getDrone(droneUUID);
        if (drone.isEmpty()) {
            return Optional.empty();
        }
        var cargoUUID = drone.get().getPersistentDataContainer().get(cargoUUIDKey, PersistentDataType.STRING);
        if (cargoUUID == null) {
            return Optional.empty();
        }
        var entity = Bukkit.getEntity(UUID.fromString(cargoUUID));
        if (entity == null) {
            return Optional.empty();
        }
        if (entity instanceof ItemDisplay) {
            return Optional.of((ItemDisplay) entity);
        } else {
            return Optional.empty();
        }

    }

    public Optional<Interaction> getInteraction(String droneUUID) {
        var drone = getDrone(droneUUID);
        if (drone.isEmpty()) {
            return Optional.empty();
        }
        var interactEntityUUID = drone.get().getPersistentDataContainer()
                .get(interactEntityUUIDKey, PersistentDataType.STRING);
        if (interactEntityUUID != null) {
            var interactEntity = Bukkit.getEntity(UUID.fromString(interactEntityUUID));
            if (interactEntity != null) {
                return Optional.of((Interaction) interactEntity);
            }
        }
        return Optional.empty();
    }

    // 从交互实体获取无人机
    public Optional<ItemDisplay> getDroneFromInteraction(Interaction interactEntity) {
        var droneUUID = interactEntity.getPersistentDataContainer().get(droneUUIDKey, PersistentDataType.STRING);
        if (droneUUID != null) {
            return getDrone(droneUUID);
        }
        return Optional.empty();
    }

    // 判断实体是否是无人机
    public boolean isDrone(Entity entity) {
        return droneCacheMap.containsKey(entity.getUniqueId().toString());
    }

    // 判断实体是否是货物
    public boolean isCargo(Entity entity) {
        return entity.getPersistentDataContainer().has(cargoUUIDKey, PersistentDataType.STRING);
    }

    // 判断实体是否是交互实体
    public boolean isInteraction(Entity entity) {
        return entity.getPersistentDataContainer().has(droneUUIDKey, PersistentDataType.STRING);
    }

    // 匹配无人机起始建筑和目标建筑的可运输展示框
    private Optional<int[]> matchSlotIndex(ItemDisplay drone) {

        // 检查是否有起终点建筑UUID
        PersistentDataContainer pdc = drone.getPersistentDataContainer();
        if (!pdc.has(startBuildingUUIDKey, PersistentDataType.STRING)
                || !pdc.has(endBuildingUUIDKey, PersistentDataType.STRING)) {
            return Optional.empty();
        }
        String startBuildingUUID = pdc.get(startBuildingUUIDKey, PersistentDataType.STRING);
        String endBuildingUUID = pdc.get(endBuildingUUIDKey, PersistentDataType.STRING);
        if (startBuildingUUID == null || endBuildingUUID == null) {
            return Optional.empty();
        }
        // 检查是否有起终点建筑UUID以及容器模块
        FCBuilding startBuilding = BuildingManager.i().getBuilding(startBuildingUUID);
        FCBuilding endBuilding = BuildingManager.i().getBuilding(endBuildingUUID);
        if (startBuilding == null || endBuilding == null) {
            return Optional.empty();
        }
        BaseModule startContainer = startBuilding.getModule("container");
        BaseModule endContainer = endBuilding.getModule("container");
        if (startContainer == null || endContainer == null) {
            return Optional.empty();
        }
        ContainerModule sc = (ContainerModule) startContainer;
        ContainerModule ec = (ContainerModule) endContainer;
        // 检查是否有output、input容器
        List<Integer> startSlots = sc.getSlotsByTag("output");
        List<Integer> endSlots = ec.getSlotsByTag("input");
        if (startSlots.isEmpty() || endSlots.isEmpty()) {
            return Optional.empty();
        }
        // 检查是否有可运输的物品，并返回index
        for (int ss : startSlots) {
            String startItemId = sc.getSlotItemId(ss);
            int startItemCount = sc.getSlotItemCount(ss);
            if (startItemId == null || startItemCount <= 0) {
                continue;
            }
            for (int es : endSlots) {
                String endItemId = ec.getSlotItemId(es);
                int endItemCount = ec.getSlotItemCount(es);
                if (endItemId == null || endItemCount >= ec.getSlotMaxCount(es)) {
                    continue;
                }
                if (startItemId.equals(endItemId)) {
                    return Optional.of(new int[] { ss, es });
                }
            }
        }
        return Optional.empty();
    }

    // scoreboard
    private double getDroneSpeed() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneSpeedEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneSpeedEntry");
        if (droneSpeedEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 1;
        }
        return scoreboard.getObjective(droneObj).getScore(droneSpeedEntry).getScore();
    }

    private int getDroneCapacity() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneCapacityEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneCapacityEntry");
        if (droneCapacityEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 1;
        }
        return scoreboard.getObjective(droneObj).getScore(droneCapacityEntry).getScore();
    }

    private int getDroneSteam() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneSteamEntry");
        if (droneSteamEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 5;
        }
        return scoreboard.getObjective(droneObj).getScore(droneSteamEntry).getScore();
    }

    private int getDroneConsumeSteam() {
        Scoreboard scoreboard = FrostCraftBU.i().getServer().getScoreboardManager().getMainScoreboard();
        String droneObj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.DroneObj");
        String droneConsumeSteamEntry = FrostCraftBU.i().getConfig()
                .getString("ScoreboardObjectives.DroneConsumeSteamEntry");
        if (droneConsumeSteamEntry == null || droneObj == null || scoreboard.getObjective(droneObj) == null) {
            return 1;
        }
        return scoreboard.getObjective(droneObj).getScore(droneConsumeSteamEntry).getScore();
    }

    // config
    private String getDroneItemId() {
        return FrostCraftBU.i().getConfig().getString("Drone.DroneItemId", "无人机");
    }

    private int getLoadUnloadTime() {
        return FrostCraftBU.i().getConfig().getInt("Drone.LoadUnloadTime", 20);
    }

    private int getChargeTime() {
        return FrostCraftBU.i().getConfig().getInt("Drone.ChargeTime", 20);
    }

    private Vector getCargoOffset() {
        return FrostCraftBU.i().getConfig().getVector("Drone.CargoOffset", new Vector(0, 1, 0));
    }

    private Location locFromString(String locS) {

        // 提取 worldName
        String worldName = locS.replaceAll(".*name=([^}]+).*", "$1");

        // 提取 x, y, z
        double x = Double.parseDouble(locS.replaceAll(".*x=([-0-9.]+).*", "$1"));
        double y = Double.parseDouble(locS.replaceAll(".*y=([-0-9.]+).*", "$1"));
        double z = Double.parseDouble(locS.replaceAll(".*z=([-0-9.]+).*", "$1"));

        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            throw new IllegalArgumentException("World not found: " + worldName);
        }

        return new Location(world, x, y, z);
    }

    private float[] vec2YawPitch(Vector dir) {
        // 归一化，避免向量长度影响角度
        dir = dir.clone().normalize();

        // yaw 计算（注意 Minecraft 坐标系的 Z/X 定义）
        float yaw = (float) Math.toDegrees(Math.atan2(-dir.getX(), dir.getZ()));

        // pitch 计算（y 是垂直方向）
        float pitch = (float) Math.toDegrees(-Math.asin(dir.getY()));
        return new float[] { yaw, pitch };
    }

    private String getErrorMsg() {
        return FrostCraftBU.i().getConfig().getString("Drone.ErrorMsg", "位于%s的无人机发生错误！");
    }

    // drone
    public Optional<ItemDisplay> initDrone(Location loc, String startBuildingUUID, String endBuildingUUID) {
        // 检查建筑
        var startBuilding = BuildingManager.i().getBuilding(startBuildingUUID);
        var endBuilding = BuildingManager.i().getBuilding(endBuildingUUID);
        if (startBuilding == null || endBuilding == null) {
            return Optional.empty();
        }
        // 生成无人机
        var material = FrostCraftBU.i().getConfig().getString("Drone.DroneMaterial", "IRON_BLOCK");
        var customModelData = FrostCraftBU.i().getConfig().getInt("Drone.DroneCustomModelData", 0);
        var drone = loc.getWorld().spawn(loc, ItemDisplay.class, d -> {
            var droneModel = new ItemStack(Material.valueOf(material));
            droneModel.editMeta(m -> {
                m.setCustomModelData(customModelData);
            });
            d.setItemStack(droneModel);
            d.setTeleportDuration(1);
            var t = d.getTransformation();
            t.getScale().dot(0.7f, 0.7f, 0.7f);
            d.setTransformation(t);
        });
        var pdc = drone.getPersistentDataContainer();
        pdc.set(startBuildingUUIDKey, PersistentDataType.STRING, startBuildingUUID);
        pdc.set(endBuildingUUIDKey, PersistentDataType.STRING, endBuildingUUID);
        pdc.set(runningStageKey, PersistentDataType.INTEGER, -1);
        pdc.set(statusKey, PersistentDataType.INTEGER, 1);
        pdc.set(steamKey, PersistentDataType.INTEGER, getDroneSteam());
        pdc.set(steamConsumeCountKey, PersistentDataType.INTEGER, 0);
        pdc.set(pathIndexKey, PersistentDataType.INTEGER, 0);
        pdc.set(chargeStageKey, PersistentDataType.INTEGER, 0);
        pdc.set(pathStartKey, PersistentDataType.STRING, loc.toString());
        pdc.set(pathEndKey, PersistentDataType.STRING, getBuildingEdgeCenter(startBuilding).toString());
        drone.setPersistent(true);
        droneCacheMap.put(drone.getUniqueId().toString(), drone);
        droneDataMap.put(drone.getUniqueId().toString(),
                new DroneData(drone.getUniqueId().toString(), loc.getWorld().getName(),
                        loc.getX(), loc.getY(), loc.getZ()));
        // 生成交互实体
        var interactEntity = loc.getWorld().spawn(loc, Interaction.class, a -> {
            a.setInteractionHeight(1f);
            a.setInteractionWidth(1f);
            a.setCustomNameVisible(false);
            pdc.set(interactEntityUUIDKey, PersistentDataType.STRING,
                    a.getUniqueId().toString());
            a.getPersistentDataContainer().set(droneUUIDKey, PersistentDataType.STRING,
                    drone.getUniqueId().toString());
        });
        return Optional.of(drone);
    }

    public ItemDisplay initCargo(ItemDisplay drone, String itemId, int count) {
        var pdc = drone.getPersistentDataContainer();
        pdc.set(cargoIDKey, PersistentDataType.STRING, itemId);
        var cargoItem = ItemManager.getItem(itemId);
        cargoItem.setAmount(count);
        ItemDisplay cargo = (ItemDisplay) drone.getWorld().spawnEntity(
                drone.getLocation().clone().add(getCargoOffset()), EntityType.ITEM_DISPLAY);
        cargo.setItemStack(cargoItem);
        pdc.set(cargoUUIDKey, PersistentDataType.STRING, cargo.getUniqueId().toString());
        pdc.set(cargoCountKey, PersistentDataType.INTEGER, count);
        return cargo;
    }

    public ItemStack pickUpDrone(ItemDisplay drone) {
        // 从各表中删除无人机记录
        droneDataMap.remove(drone.getUniqueId().toString());
        droneCacheMap.remove(drone.getUniqueId().toString());
        droneLoadUnloadTimeMap.remove(drone.getUniqueId().toString());
        droneChargeTimeMap.remove(drone.getUniqueId().toString());
        droneErrorMsgTimeMap.remove(drone.getUniqueId().toString());
        // 删除无人机实体
        drone.remove();
        // 删除货物实体
        getCargo(drone.getUniqueId().toString()).ifPresent(cargo -> cargo.remove());
        // 删除交互实体
        getInteraction(drone.getUniqueId().toString()).ifPresent(interactEntity -> interactEntity.remove());
        // 返回无人机物品
        var droneItem = ItemManager.getItem(getDroneItemId());
        return droneItem;
    }

    public void froceRemoveDrone(ItemDisplay drone) {
        // check if is drone
        if (drone.getPersistentDataContainer().has(startBuildingUUIDKey, PersistentDataType.STRING)) {
            pickUpDrone(drone);
        }
    }

    /**
     * 打开无人机物品UI，展示无人机的起始建筑设定按钮、目标建筑设定按钮、部署按钮
     * 
     * @param droneItem
     * @return
     */
    public ChestMenu getDroneItemUI(ItemStack droneItem) {
        ChestMenu ui = ChestMenu.builder(1).title("无人机").build();
        ItemMeta meta = droneItem.getItemMeta();
        PersistentDataContainer pdc = meta.getPersistentDataContainer();
        String startBuildingUUID = pdc.get(startBuildingUUIDKey, PersistentDataType.STRING);
        String endBuildingUUID = pdc.get(endBuildingUUIDKey, PersistentDataType.STRING);
        ItemStack startButton = ItemManager.getItem("drone_start_button");
        List<TextComponent> startButtonLore = new ArrayList<TextComponent>();
        startButtonLore.add(Component.text("点击设定无人机起始建筑").color(NamedTextColor.GREEN));
        if (startBuildingUUID != null) {
            FCBuilding startBuilding = BuildingManager.i().getBuilding(startBuildingUUID);
            if (startBuilding == null) {
                pdc.set(startBuildingUUIDKey, PersistentDataType.STRING, null);
                droneItem.setItemMeta(meta);
            } else {
                // log
                startButtonLore.add(Component.text("当前起始建筑: " + startBuilding.getBuildingConfig().getId() + "("
                        + startBuilding.getCenter().toVector().toString() + ")"));
            }
        }
        startButton.lore(startButtonLore);
        ui.getSlot(0).setItem(startButton);
        ui.getSlot(0).setClickHandler((p, info) -> {
            ui.close(p);
            // 检查当前玩家是否处于一个建筑中
            FCBuilding building = BuildingManager.i().getBuildingAt(p.getLocation());
            if (building == null) {
                p.sendMessage("§c你必须处于一个建筑中才能设定无人机起始建筑！");
                return;
            }
            // 检查当前建筑是否拥有container模块
            if (building.getModule(ModuleManager.ModuleType.ContainerModule.getName()) == null) {
                p.sendMessage("§c当前建筑没有物品容器，无法作为无人机起始建筑！");
                return;
            }
            pdc.set(startBuildingUUIDKey, PersistentDataType.STRING, building.getUnitUUID());
            droneItem.setItemMeta(meta);
            p.sendMessage("§a无人机起始建筑设定成功！" + droneItem.getItemMeta().getPersistentDataContainer()
                    .get(startBuildingUUIDKey, PersistentDataType.STRING));
        });

        // 目标建筑设定按钮
        var endButton = ItemManager.getItem("drone_end_button");
        List<TextComponent> endButtonLore = new ArrayList<TextComponent>();
        endButtonLore.add(Component.text("点击设定无人机目标建筑").color(NamedTextColor.GREEN));
        if (endBuildingUUID != null) {
            FCBuilding endBuilding = BuildingManager.i().getBuilding(endBuildingUUID);
            if (endBuilding == null) {
                pdc.set(endBuildingUUIDKey, PersistentDataType.STRING, null);
                droneItem.setItemMeta(meta);
            } else {
                endButtonLore.add(Component.text("当前目标建筑: " + endBuilding.getBuildingConfig().getId() + "("
                        + endBuilding.getCenter().toVector().toString() + ")"));
            }
        }
        endButton.lore(endButtonLore);
        ui.getSlot(1).setItem(endButton);
        ui.getSlot(1).setClickHandler((p, info) -> {
            ui.close(p);
            // 检查当前玩家是否处于一个建筑中
            FCBuilding building = BuildingManager.i().getBuildingAt(p.getLocation());
            if (building == null) {
                p.sendMessage("§c你必须处于一个建筑中才能设定无人机目标建筑！");
                return;
            }
            // 检查当前建筑是否拥有container模块
            if (building.getModule(ModuleManager.ModuleType.ContainerModule.getName()) == null) {
                p.sendMessage("§c当前建筑没有物品容器，无法作为无人机目标建筑！");
                return;
            }
            pdc.set(endBuildingUUIDKey, PersistentDataType.STRING, building.getUnitUUID());
            droneItem.setItemMeta(meta);
            p.sendMessage("§a无人机目标建筑设定成功！");
        });
        // 部署按钮
        var deployButton = ItemManager.getItem("drone_deploy_button");
        var deployButtonLore = List.of(Component.text("在脚下部署无人机").color(NamedTextColor.GREEN));
        deployButton.lore(deployButtonLore);
        ui.getSlot(2).setItem(deployButton);
        ui.getSlot(2).setClickHandler((p, info) -> {
            ui.close(p);
            if (startBuildingUUID == null || endBuildingUUID == null) {
                p.sendMessage("§c你必须先设定无人机起始建筑和目标建筑才能部署无人机！");
                return;
            }
            if (startBuildingUUID.equals(endBuildingUUID)) {
                p.sendMessage("§c无人机起始建筑和目标建筑不能相同！");
                return;
            }
            initDrone(p.getLocation(), startBuildingUUID, endBuildingUUID);
            p.sendMessage("§a无人机部署成功！");
            p.getInventory().remove(droneItem);
        });
        return ui;
    }

    /**
     * 打开无人机实体UI，展示无人机的
     * 1. 起终点建筑
     * 2. 当前各种状态(蒸汽值, status, runningStage, chargeStage, 当前起终点坐标)
     * 3. 拿起货物按钮（如果有货物）
     * 4. 拿起无人机按钮
     * 
     * 
     * @param drone
     * @return
     */
    public ChestMenu getDroneUI(ItemDisplay drone) {
        ChestMenu ui = ChestMenu.builder(1).title("无人机").build();
        var pdc = drone.getPersistentDataContainer();
        // log pdc
        pdc.getKeys().forEach((k) -> {
            FrostCraftBU.i().getLogger().info("pdc key: " + k.toString());
        });
        var startBuildingUUID = pdc.get(startBuildingUUIDKey, PersistentDataType.STRING);
        var endBuildingUUID = pdc.get(endBuildingUUIDKey, PersistentDataType.STRING);
        var startBuilding = BuildingManager.i().getBuilding(startBuildingUUID);
        var endBuilding = BuildingManager.i().getBuilding(endBuildingUUID);
        // 状态显示按钮
        var statusButton = ItemManager.getItem("drone_status_button");
        var statusLore = new ArrayList<TextComponent>();
        statusLore.add(
                Component.text("起始建筑: " + (startBuilding != null ? startBuilding.getBuildingConfig().getId() : "无"))
                        .color(NamedTextColor.GREEN));
        statusLore
                .add(Component.text("目标建筑: " + (endBuilding != null ? endBuilding.getBuildingConfig().getId() : "无"))
                        .color(NamedTextColor.GREEN));
        statusLore.add(
                Component.text("蒸汽值: " + pdc.get(steamKey, PersistentDataType.INTEGER)).color(NamedTextColor.GREEN));

        String statusText = switch (pdc.get(statusKey, PersistentDataType.INTEGER)) {
            case 0 -> "空闲";
            case 1 -> "运行中";
            case 2 -> "装卸货";
            case 3 -> "充电中";
            case 4 -> "找不到路径";
            case 5 -> "路径阻塞";
            default -> "未知";
        };
        statusLore.add(Component.text("状态: " + statusText).color(NamedTextColor.GREEN));

        String runningStageText = switch (pdc.get(runningStageKey, PersistentDataType.INTEGER)) {
            case -1 -> "初始化到起始建筑";
            case 0 -> "起始展示框到起始建筑";
            case 1 -> "起始建筑到目标建筑";
            case 2 -> "目标建筑到目标展示框";
            case 3 -> "目标展示框到目标建筑";
            case 4 -> "目标建筑到起始建筑";
            case 5 -> "起始建筑到起始展示框";
            default -> "未知";
        };
        statusLore.add(Component.text("运行阶段: " + runningStageText).color(NamedTextColor.GREEN));

        String chargeStageText = switch (pdc.get(chargeStageKey, PersistentDataType.INTEGER)) {
            case 0 -> "不需充电";
            case 1 -> "前往充电桩建筑";
            case 2 -> "从建筑到充电桩";
            case 3 -> "从充电桩返回建筑";
            default -> "未知";
        };
        statusLore.add(Component.text("充电阶段: " + chargeStageText).color(NamedTextColor.GREEN));

        statusLore.add(Component.text("当前起点: " + pdc.get(pathStartKey, PersistentDataType.STRING))
                .color(NamedTextColor.GREEN));
        statusLore.add(
                Component.text("当前终点: " + pdc.get(pathEndKey, PersistentDataType.STRING)).color(NamedTextColor.GREEN));

        statusButton.lore(statusLore);
        ui.getSlot(0).setItem(statusButton);

        // 拿起货物按钮（如果有货物）
        if (pdc.has(cargoUUIDKey, PersistentDataType.STRING)) {
            var cargoButton = ItemManager.getItem("drone_cargo_button");
            var cargoLore = List.of(
                    Component.text("货物ID: " + pdc.get(cargoIDKey, PersistentDataType.STRING))
                            .color(NamedTextColor.GREEN),
                    Component.text("数量: " + pdc.get(cargoCountKey, PersistentDataType.INTEGER))
                            .color(NamedTextColor.GREEN),
                    Component.text("点击拿走货物").color(NamedTextColor.GREEN));
            cargoButton.lore(cargoLore);
            ui.getSlot(1).setItem(cargoButton);
            ui.getSlot(1).setClickHandler((p, info) -> {
                ui.close(p);
                var cargo = getCargo(drone.getUniqueId().toString());
                if (cargo.isPresent()) {
                    cargo.get().remove();
                    var item = ItemManager.getItem(pdc.get(cargoIDKey, PersistentDataType.STRING));
                    item.setAmount(pdc.get(cargoCountKey, PersistentDataType.INTEGER));
                    p.getWorld().dropItem(p.getLocation(), item);
                    pdc.remove(cargoUUIDKey);
                    pdc.remove(cargoIDKey);
                    pdc.remove(cargoCountKey);
                }
            });
        }

        // 拿起无人机按钮
        var pickupButton = ItemManager.getItem("drone_pickup_button");
        var pickupLore = List.of(Component.text("点击拿起无人机").color(NamedTextColor.GREEN));
        pickupButton.lore(pickupLore);
        ui.getSlot(2).setItem(pickupButton);
        ui.getSlot(2).setClickHandler((p, info) -> {
            ui.close(p);
            var droneItem = pickUpDrone(drone);
            p.getWorld().dropItem(p.getLocation(), droneItem);
        });
        return ui;
    }

    // tick
    private void tickDrone(String uuid) {
        var droneOpt = getDrone(uuid);
        if (!droneOpt.isPresent()) {
            return;
        }
        ItemDisplay drone = droneOpt.get();
        var pdc = drone.getPersistentDataContainer();
        var startBuilding = BuildingManager.i()
                .getBuilding(pdc.get(startBuildingUUIDKey, PersistentDataType.STRING));
        var endBuilding = BuildingManager.i().getBuilding(pdc.get(endBuildingUUIDKey, PersistentDataType.STRING));
        if (startBuilding == null || endBuilding == null) {
            pdc.set(statusKey, PersistentDataType.INTEGER, 4);
            return;
        }
        // 1. 检查是否处于空闲、装卸货、充电中、找不到路径、路径阻塞状态，并执行对应逻辑。装卸货、充电完成时，重新设定充电阶段及运行阶段，返回
        if (!pdc.has(statusKey, PersistentDataType.INTEGER)) {
            pdc.set(statusKey, PersistentDataType.INTEGER, 4);
            return;
        }
        int status = pdc.get(statusKey, PersistentDataType.INTEGER);
        switch (status) {
            case 0: // 空闲
                idle(drone, startBuilding);
                return;
            case 1: // 常规运行中
                break;
            case 2: // 装卸货
                int startFrameIndex = pdc.get(startFrameIndexKey, PersistentDataType.INTEGER);
                int endFrameIndex = pdc.get(endFrameIndexKey, PersistentDataType.INTEGER);
                loadUnload(drone, startBuilding, endBuilding, startFrameIndex, endFrameIndex);
                return;
            case 3: // 充电中
                charging(drone);
                return;
            case 4: // 找不到路径
                errMsg(drone);
                return;
            case 5: // 路径阻塞
                errMsg(drone);
                return;
            default:
                return;
        }
        // 2. 检查是否耗尽蒸汽，如果耗尽，则进入充电阶段1，遍历建筑，寻找最近的充电桩，设定特殊路径起终点，返回

        int steam = pdc.get(steamKey, PersistentDataType.INTEGER);
        if (steam <= 0 && pdc.get(chargeStageKey, PersistentDataType.INTEGER) == 0) {
            pdc.set(chargeStageKey, PersistentDataType.INTEGER, 1);
            var nearChargeBuildingOpt = getNearestChargeBuilding(drone.getLocation());
            if (nearChargeBuildingOpt.isPresent()) {
                var nearChargeBuilding = nearChargeBuildingOpt.get();
                pdc.set(pathStartKey, PersistentDataType.STRING, drone.getLocation().toString());
                pdc.set(pathEndKey, PersistentDataType.STRING,
                        getBuildingEdgeCenter(BuildingManager.i().getBuilding(nearChargeBuilding)).toString());
            } else {
                pdc.set(statusKey, PersistentDataType.INTEGER, 4);
            }
            return;
        }

        // 3. 检查目标路径是否存在，如果不存在则检查genPathMap，没有则开始生成路径，路径不通或建筑丢失则进入找不到路径状态，公屏播报，返回
        // 根据运行阶段查找对应路径
        Location startLoc = locFromString(pdc.get(pathStartKey, PersistentDataType.STRING));
        Location endLoc = locFromString(pdc.get(pathEndKey, PersistentDataType.STRING));

        // 充电运行状态重新寻路判断
        int chargeStage = pdc.get(chargeStageKey, PersistentDataType.INTEGER);
        // chargingStage(drone);
        FCBuilding chargeBuilding = BuildingManager.i()
                .getBuilding(pdc.get(chargeBuildingUUIDKey, PersistentDataType.STRING));
        if (chargeStage == 1 || chargeStage == 2 || chargeStage == 3) {
            if (chargeBuilding == null) {
                if (chargeStage == 3) {
                    // 直接结束充电阶段，从无人机当前位置开始运行
                    pdc.set(chargeStageKey, PersistentDataType.INTEGER, 0);
                    pdc.set(pathIndexKey, PersistentDataType.INTEGER, 0);
                    switch (status) {
                        case 0, 1, 2, 3:
                            pdc.set(runningStageKey, PersistentDataType.INTEGER, 1);
                            pdc.set(pathStartKey, PersistentDataType.STRING, drone.getLocation().toString());
                            pdc.set(pathEndKey, PersistentDataType.STRING,
                                    getBuildingEdgeCenter(endBuilding).toString());
                            break;
                        case 4, 5:
                            pdc.set(runningStageKey, PersistentDataType.INTEGER, 4);
                            pdc.set(pathStartKey, PersistentDataType.STRING, drone.getLocation().toString());
                            pdc.set(pathEndKey, PersistentDataType.STRING,
                                    getBuildingEdgeCenter(startBuilding).toString());
                            break;
                        default:
                            break;
                    }
                    return;
                }
                var newCharge = getNearestChargeBuilding(drone.getLocation());
                if (newCharge.isPresent()) {
                    pdc.set(chargeBuildingUUIDKey, PersistentDataType.STRING, newCharge.get());
                    pdc.set(pathIndexKey, PersistentDataType.INTEGER, 0);
                    pdc.set(pathStartKey, PersistentDataType.STRING,
                            drone.getLocation().toString());
                    pdc.set(pathEndKey, PersistentDataType.STRING,
                            getBuildingEdgeCenter(BuildingManager.i().getBuilding(newCharge.get())).toString());
                    pdc.set(chargeStageKey, PersistentDataType.INTEGER, 1);
                } else {
                    pdc.set(statusKey, PersistentDataType.INTEGER, 4);
                }
                return;
            }
            return;
        }

        // 检查路径是否存在
        var pathKey = new PathKey(startLoc, endLoc);
        if (!pathMap.containsKey(pathKey)) {
            if (!generatingPathMap.containsKey(pathKey)) {
                DronePathfindingService dps = FrostCraftBU.getDronePathfindingService();
                generatingPathMap.put(pathKey, dps.findPathAsync(startLoc, endLoc));
                // log
                FrostCraftBU.i().getLogger().info("开始生成路径: " + pathKey.toString());
            } else {
                var future = generatingPathMap.get(pathKey);
                if (future.isDone()) {
                    try {
                        var result = future.get();
                        // log
                        FrostCraftBU.i().getLogger().info("路径生成完成: " + pathKey.toString() + " " + result.toString());
                        if (result.isSuccessful()) {
                            pathMap.put(pathKey, result);
                        } else {
                            pdc.set(statusKey, PersistentDataType.INTEGER, 4);
                        }
                        generatingPathMap.remove(pathKey);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    // log 生成中
                    FrostCraftBU.i().getLogger().info("路径生成中: " + pathKey.toString());
                }
            }
            return;
        }
        DronePathResult dronePathResult = pathMap.get(pathKey);
        boolean isReverse = dronePathResult.getStartLocation().equals(endLoc);

        // 4. 检查是否到达路径终点，如果到达则综合分析当前状态进入下一个阶段，检查当前完成的路径是否是临时路径并删除。
        var pathIndex = pdc.get(pathIndexKey, PersistentDataType.INTEGER);
        var speed = getDroneSpeed();
        Vector direction = PathSmoothingUtil.calculateMovementDirection(
                pathIndex, speed, pathMap.get(pathKey).getRawPath(), PathSmoothingUtil.SmoothingType.CATMULL_ROM,
                isReverse).get();

        double progress = PathSmoothingUtil.calculatePathProgress(pathIndex, speed, pathMap.get(pathKey).getRawPath(),
                isReverse);
        if (progress >= 1) {
            // log
            FrostCraftBU.i().getLogger().info("无人机" + uuid + "到达路径终点,当前运行阶段: " + status);
            pdc.set(pathIndexKey, PersistentDataType.INTEGER, 0);
            // 根据下一阶段启动对应的逻辑
            int runningStage = pdc.get(runningStageKey, PersistentDataType.INTEGER);
            // 充电阶段
            if (chargeStage != 0) {
                switch (chargeStage) {
                    case 1:
                        pdc.set(chargeStageKey, PersistentDataType.INTEGER, 2);
                        pdc.set(pathStartKey, PersistentDataType.STRING,
                                getBuildingEdgeCenter(chargeBuilding).toString());
                        pdc.set(pathEndKey, PersistentDataType.STRING,
                                getChargeLocation(chargeBuilding).get().toString());
                        break;
                    case 2:
                        pdc.set(statusKey, PersistentDataType.INTEGER, 3);
                        break;
                    case 3:
                        pdc.set(chargeStageKey, PersistentDataType.INTEGER, 0);
                        switch (runningStage) {
                            case 0, 1, 2, 3:
                                pdc.set(runningStageKey, PersistentDataType.INTEGER, 1);
                                pdc.set(pathStartKey, PersistentDataType.STRING,
                                        getBuildingEdgeCenter(chargeBuilding).toString());
                                pdc.set(pathEndKey, PersistentDataType.STRING,
                                        getBuildingEdgeCenter(endBuilding).toString());
                                break;
                            case 4, 5:
                                pdc.set(runningStageKey, PersistentDataType.INTEGER, 4);
                                pdc.set(pathStartKey, PersistentDataType.STRING,
                                        getBuildingEdgeCenter(chargeBuilding).toString());
                                pdc.set(pathEndKey, PersistentDataType.STRING,
                                        getBuildingEdgeCenter(startBuilding).toString());
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        break;
                }
            }
            // 普通运行阶段
            switch (runningStage) {
                case -1:
                    // log
                    FrostCraftBU.i().getLogger().info("无人机" + uuid + "到达起始建筑");
                    // 到达起始建筑，进入待机状态检测可用运输
                    pdc.set(statusKey, PersistentDataType.INTEGER, 0);
                    break;
                case 0:
                    // log
                    FrostCraftBU.i().getLogger().info("无人机" + uuid + "到达起始建筑");
                    // 到达起始建筑，向目标建筑移动
                    pdc.set(runningStageKey, PersistentDataType.INTEGER, 1);
                    pdc.set(pathStartKey, PersistentDataType.STRING,
                            getBuildingEdgeCenter(startBuilding).toString());
                    pdc.set(pathEndKey, PersistentDataType.STRING,
                            getBuildingEdgeCenter(endBuilding).toString());
                    break;
                case 1:
                    // log
                    FrostCraftBU.i().getLogger().info("无人机" + uuid + "从起始建筑到达目标建筑");
                    // 到达目标建筑，向目标展示框移动
                    int endFrameIndex = pdc.get(endFrameIndexKey, PersistentDataType.INTEGER);
                    pdc.set(runningStageKey, PersistentDataType.INTEGER, 2);
                    pdc.set(pathStartKey, PersistentDataType.STRING,
                            getBuildingEdgeCenter(endBuilding).toString());
                    pdc.set(pathEndKey, PersistentDataType.STRING,
                            getFrameLocation(endBuilding, endFrameIndex).toString());
                    break;
                case 2:
                    // log
                    FrostCraftBU.i().getLogger().info("无人机" + uuid + "到达目标展示框");
                    // 到达目标展示框，进入装卸货状态
                    pdc.set(statusKey, PersistentDataType.INTEGER, 2);
                    break;
                case 3:
                    // log
                    FrostCraftBU.i().getLogger().info("无人机" + uuid + "完成卸货到达目标建筑");
                    // 到达目标建筑，向起始建筑移动
                    pdc.set(runningStageKey, PersistentDataType.INTEGER, 4);
                    pdc.set(pathStartKey, PersistentDataType.STRING,
                            getBuildingEdgeCenter(endBuilding).toString());
                    pdc.set(pathEndKey, PersistentDataType.STRING, getBuildingEdgeCenter(startBuilding).toString());
                    break;
                case 4:
                    // log
                    FrostCraftBU.i().getLogger().info("无人机" + uuid + "从目标建筑到达起始建筑");
                    // 到达起始建筑，进入待机状态检测可用运输
                    pdc.set(statusKey, PersistentDataType.INTEGER, 0);
                    break;
                case 5:
                    // log
                    FrostCraftBU.i().getLogger().info("无人机" + uuid + "到达起始展示框");
                    // 到达起始展示框，进入装货
                    pdc.set(statusKey, PersistentDataType.INTEGER, 2);
                    break;
                default:
                    break;
            }
            return;
        }

        // 5. 如果下一个路径点是实体方块则进入路径阻塞状态，删除当前路径，公屏播报
        PathSmoothingUtil.calculateRealtimePosition(pathIndex, speed, pathMap.get(pathKey).getRawPath(),
                PathSmoothingUtil.SmoothingType.CATMULL_ROM, 10, isReverse).ifPresent(location -> {
                    if (!location.getBlock().isPassable()) {
                        pdc.set(statusKey, PersistentDataType.INTEGER, 5);
                        pathMap.remove(pathKey);
                        return;
                    }
                    // 6. 向下一个路径点移动，并增加蒸汽消耗计数，如果计数达到一分钟则消耗蒸汽，返回
                    drone.teleport(location);
                    var yawPitch = vec2YawPitch(direction);
                    drone.setRotation(yawPitch[0], 0);
                    updateDroneData(uuid, location.getX(), location.getY(), location.getZ());
                    // 移动货物
                    getCargo(uuid).ifPresent(cargo -> cargo.teleport(location.clone().add(getCargoOffset())));
                    // 移动交互实体
                    getInteraction(uuid).ifPresent(interactEntity -> interactEntity.teleport(location));
                    pdc.set(pathIndexKey, PersistentDataType.INTEGER, pathIndex + 1);
                    pdc.set(steamConsumeCountKey, PersistentDataType.INTEGER,
                            pdc.get(steamConsumeCountKey, PersistentDataType.INTEGER) + 1);
                    if (pdc.get(steamConsumeCountKey, PersistentDataType.INTEGER) >= 20 * 60) {
                        pdc.set(steamKey, PersistentDataType.INTEGER,
                                pdc.get(steamKey, PersistentDataType.INTEGER) - getDroneConsumeSteam());
                        pdc.set(steamConsumeCountKey, PersistentDataType.INTEGER, 0);
                    }
                });
    }

    /**
     * 
     * @param drone
     * @param type
     */
    private void errMsg(ItemDisplay drone) {
        var uuid = drone.getUniqueId().toString();
        // 计时
        var currentTime = droneErrorMsgTimeMap.getOrDefault(uuid, 0);
        if (currentTime < 20 * 60) {
            droneErrorMsgTimeMap.put(uuid, currentTime + 1);
            return;
        }
        droneErrorMsgTimeMap.put(uuid, 0);
        Bukkit.broadcast(Component.text(String.format(getErrorMsg(), drone.getLocation().toVector().toString())));

    }

    private void idle(ItemDisplay drone, FCBuilding startBuilding) {
        var matchOpt = matchSlotIndex(drone);
        if (!matchOpt.isPresent()) {
            return;
        }
        // 设置pdc
        var pdc = drone.getPersistentDataContainer();
        pdc.set(startFrameIndexKey, PersistentDataType.INTEGER, matchOpt.get()[0]);
        pdc.set(endFrameIndexKey, PersistentDataType.INTEGER, matchOpt.get()[1]);
        pdc.set(runningStageKey, PersistentDataType.INTEGER, 5);
        pdc.set(statusKey, PersistentDataType.INTEGER, 1);
        pdc.set(pathStartKey, PersistentDataType.STRING, getBuildingEdgeCenter(startBuilding).toString());
        pdc.set(pathEndKey, PersistentDataType.STRING, getFrameLocation(startBuilding, matchOpt.get()[0]).toString());

    }

    private void loadUnload(ItemDisplay drone, FCBuilding startBuilding, FCBuilding endBuilding, int startFrameIndex,
            int endFrameIndex) {
        var time = getLoadUnloadTime();
        var uuid = drone.getUniqueId().toString();
        var currentTime = droneLoadUnloadTimeMap.getOrDefault(uuid, 0);
        if (currentTime >= time) {
            // 完成装卸货
            // 获取建筑容器模块
            droneLoadUnloadTimeMap.put(uuid, 0);
            var pdc = drone.getPersistentDataContainer();
            if (pdc.get(runningStageKey, PersistentDataType.INTEGER) == 5) {
                // runningStage = 5 装货
                ContainerModule startContainer = (ContainerModule) startBuilding.getModule("container");
                int dronecap = getDroneCapacity();
                int count = Math.min(startContainer.getSlotItemCount(startFrameIndex), dronecap);
                String itemId = startContainer.getSlotItemId(startFrameIndex);
                startContainer.cosumeItem(startFrameIndex, count);
                initCargo(drone, itemId, count);
                pdc.set(statusKey, PersistentDataType.INTEGER, 1);
                // 从起始展示框到起始建筑
                pdc.set(runningStageKey, PersistentDataType.INTEGER, 0);
                pdc.set(pathStartKey, PersistentDataType.STRING,
                        getFrameLocation(startBuilding, startFrameIndex).toString());
                pdc.set(pathEndKey, PersistentDataType.STRING,
                        getBuildingEdgeCenter(startBuilding).toString());
            } else {
                // runningStage = 2 卸货
                ContainerModule endContainer = (ContainerModule) endBuilding.getModule("container");
                if (pdc.has(cargoUUIDKey, PersistentDataType.STRING)
                        && pdc.has(cargoIDKey, PersistentDataType.STRING)
                        && pdc.has(cargoCountKey, PersistentDataType.INTEGER)) {
                    var itemId = pdc.get(cargoIDKey, PersistentDataType.STRING);
                    var count = pdc.get(cargoCountKey, PersistentDataType.INTEGER);
                    endContainer.produceItem(endFrameIndex, count);
                    var cargoUUID = pdc.get(cargoUUIDKey, PersistentDataType.STRING);
                    var cargo = (ItemDisplay) Bukkit.getEntity(UUID.fromString(cargoUUID));
                    if (cargo != null) {
                        cargo.remove();
                    }
                    pdc.remove(cargoUUIDKey);
                    pdc.remove(cargoIDKey);
                    pdc.remove(cargoCountKey);
                }
                pdc.set(statusKey, PersistentDataType.INTEGER, 1);
                pdc.set(runningStageKey, PersistentDataType.INTEGER, 3);
                pdc.set(pathStartKey, PersistentDataType.STRING,
                        getFrameLocation(endBuilding, endFrameIndex).toString());
                pdc.set(pathEndKey, PersistentDataType.STRING, getBuildingEdgeCenter(endBuilding).toString());
            }
        } else {
            droneLoadUnloadTimeMap.put(uuid, currentTime + 1);
        }
    }

    private void charging(ItemDisplay drone) {
        var uuid = drone.getUniqueId().toString();
        var pdc = drone.getPersistentDataContainer();
        if (!pdc.has(chargeStageKey, PersistentDataType.INTEGER)) {
            return;
        }
        if (droneChargeTimeMap.getOrDefault(uuid, 0) >= getChargeTime()) {
            // 充电完成
            pdc.set(steamKey, PersistentDataType.INTEGER, getDroneSteam());
            pdc.set(chargeStageKey, PersistentDataType.INTEGER, 3);
            pdc.set(statusKey, PersistentDataType.INTEGER, 1);
            droneChargeTimeMap.put(uuid, 0);
            return;
        }
        droneChargeTimeMap.put(uuid, droneChargeTimeMap.getOrDefault(uuid, 0) + 1);
    }

    public class PathKey {
        private Location start;
        private Location end;
        private boolean isTemp;

        public PathKey(Location start, Location end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            PathKey pathKey = (PathKey) o;
            return (start.equals(pathKey.start) && end.equals(pathKey.end)) ||
                    (start.equals(pathKey.end) && end.equals(pathKey.start));
        }

        @Override
        public int hashCode() {
            return start.hashCode() + end.hashCode();
        }

        public boolean isReverse(PathKey other) {
            return (start.equals(other.end) && end.equals(other.start));
        }

        public boolean isTemp() {
            return isTemp;
        }

        public void setTemp(boolean temp) {
            isTemp = temp;
        }

    }

}
