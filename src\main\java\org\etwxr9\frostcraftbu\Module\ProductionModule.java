package org.etwxr9.frostcraftbu.Module;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Module.Behavior.IProduction;
import org.etwxr9.frostcraftbu.Recipe.RecipeConfig;
import org.etwxr9.frostcraftbu.Recipe.RecipeManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class ProductionModule extends BaseModule implements IProduction {
    // 配置数据类
    private class ProductionLineConfig {
        List<Integer> inputSlots;
        List<Integer> outputSlots;
        List<String> availableRecipes;
    }

    // 运行时状态类
    private class ProductionLineState {
        String currentRecipe;
        int progress;
    }

    // 产线类，组合配置和状态
    private class ProductionLine {
        ProductionLineConfig config;
        ProductionLineState state;

        ProductionLine(ProductionLineConfig config) {
            this.config = config;
            this.state = new ProductionLineState();
            this.state.currentRecipe = null;
            this.state.progress = 0;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProductionModuleSaveData extends BaseModuleSaveData {
        private static class ProductionLineStateSaveData {
            private String currentRecipe;
            private int progress;

            public ProductionLineStateSaveData() {
            }

            public ProductionLineStateSaveData(ProductionLineState state) {
                this.currentRecipe = state.currentRecipe;
                this.progress = state.progress;
            }
        }

        private List<ProductionLineStateSaveData> productionLineStates;

        public ProductionModuleSaveData() {
        }

        public ProductionModuleSaveData(List<ProductionLineStateSaveData> states) {
            this.productionLineStates = states;
        }

        public List<ProductionLineStateSaveData> getProductionLineStates() {
            return productionLineStates;
        }

        @Override
        public String getModuleTypeId() {
            return ModuleManager.ModuleType.ProductionModule.getName();
        }
    }

    private List<ProductionLine> productionLines = new ArrayList<>();
    private ContainerModule containerModule;

    public ProductionModule(FCBuilding building) {
        super(building);
    }

    @Override
    public void onLoad(boolean first) {
        containerModule = (ContainerModule) building.getModule("container");
        if (containerModule == null) {
            throw new IllegalStateException("ProductionModule requires ContainerModule");
        }

        // 从配置加载产线配置
        List<Map<?, ?>> lines = getModuleConfig().getMapList("lines");
        for (Map<?, ?> line : lines) {
            ProductionLineConfig config = new ProductionLineConfig();
            config.inputSlots = (List<Integer>) line.get("input");
            config.outputSlots = (List<Integer>) line.get("output");
            config.availableRecipes = (List<String>) line.get("recipe");

            productionLines.add(new ProductionLine(config));
        }
    }

    @Override
    public void tick() {
        for (int i = 0; i < productionLines.size(); i++) {
            ProductionLine line = productionLines.get(i);
            if (line.state.currentRecipe == null)
                continue;

            if (canProduce(i)) {
                RecipeConfig recipe = RecipeManager.i().getRecipe(line.state.currentRecipe);
                line.state.progress++;

                if (line.state.progress >= recipe.getTime()) {
                    produce(i);
                    line.state.progress = 0;
                }
            }
        }
    }

    private void produce(int lineIndex) {
        ProductionLine line = productionLines.get(lineIndex);
        RecipeConfig recipe = RecipeManager.i().getRecipe(line.state.currentRecipe);

        // 消耗输入物品
        for (Map.Entry<String, Integer> input : recipe.getInput().entrySet()) {
            int remaining = input.getValue();
            for (int slotId : line.config.inputSlots) {
                if (remaining <= 0)
                    break;

                String slotItemId = containerModule.getSlotItemId(slotId);
                if (input.getKey().equals(slotItemId)) {
                    int slotCount = containerModule.getSlotItemCount(slotId);
                    int consume = Math.min(remaining, slotCount);
                    containerModule.cosumeItem(slotId, consume);
                }
            }
        }

        // 添加输出物品
        for (Map.Entry<String, Integer> output : recipe.getOutput().entrySet()) {
            int remaining = output.getValue();
            for (int slotId : line.config.outputSlots) {
                if (remaining <= 0)
                    break;

                String slotItemId = containerModule.getSlotItemId(slotId);
                int slotMaxCount = containerModule.getSlotMaxCount(slotId);

                if (slotItemId == null || output.getKey().equals(slotItemId)) {
                    int slotCount = containerModule.getSlotItemCount(slotId);
                    int space = slotMaxCount - slotCount;
                    int add = Math.min(remaining, space);
                    containerModule.produceItem(slotId, add);
                    remaining -= add;
                }
            }
        }

        // 消耗蒸汽
        var comsumeSteamobj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamObj");
        var comsumeSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamEntry");
        var comsumeSteamValue = recipe.getSteam();
        if (comsumeSteamobj != null && comsumeSteamEntry != null) {
            try {
                Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                Objective objective = scoreboard.getObjective(comsumeSteamobj);
                if (objective != null) {
                    Score score = objective.getScore(comsumeSteamEntry);
                    score.setScore(score.getScore() - comsumeSteamValue);
                } else {
                    FrostCraftBU.i().getLogger().warning("记分板目标 '" + comsumeSteamobj + "' 不存在！请在游戏中创建它。");
                }
            } catch (Exception e) {
                FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + comsumeSteamobj + "): ", e);
            }
        }
    }

    @Override
    public boolean setRecipe(int lineIndex, String recipeId) {
        if (lineIndex >= productionLines.size())
            return false;

        ProductionLine line = productionLines.get(lineIndex);
        // if (!line.config.availableRecipes.contains(recipeId))
        //     return false;

        line.state.currentRecipe = recipeId;
        line.state.progress = 0;

        // 更新容器slot的物品类型
        if (recipeId != null) {
            RecipeConfig recipe = RecipeManager.i().getRecipe(recipeId);
            // 设置输入槽
            for (int i = 0; i < line.config.inputSlots.size(); i++) {
                int slotId = line.config.inputSlots.get(i);
                if (i < recipe.getInput().size()) {
                    String itemId = new ArrayList<>(recipe.getInput().keySet()).get(i);
                    containerModule.setSlotItem(slotId, itemId, 0);
                }
            }
            // 设置输出槽
            for (int i = 0; i < line.config.outputSlots.size(); i++) {
                int slotId = line.config.outputSlots.get(i);
                if (i < recipe.getOutput().size()) {
                    String itemId = new ArrayList<>(recipe.getOutput().keySet()).get(i);
                    containerModule.setSlotItem(slotId, itemId, 0);
                }
            }
        } else {
            for (int slotId : line.config.inputSlots) {
                containerModule.clearSlot(slotId);
            }
            for (int slotId : line.config.outputSlots) {
                containerModule.clearSlot(slotId);
            }
        }
        return true;
    }

    @Override
    public String getRecipe(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return null;
        return productionLines.get(lineIndex).state.currentRecipe;
    }

    @Override
    public int getProgress(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return 0;
        return productionLines.get(lineIndex).state.progress;
    }

    @Override
    public boolean canProduce(int lineIndex) {
        if (lineIndex >= productionLines.size())
            return false;

        ProductionLine line = productionLines.get(lineIndex);
        if (line.state.currentRecipe == null)
            return false;

        RecipeConfig recipe = RecipeManager.i().getRecipe(line.state.currentRecipe);

        if (recipe == null) {
            return false;
        }

        // 检查输入物品是否足够
        for (Map.Entry<String, Integer> input : recipe.getInput().entrySet()) {
            int total = 0;
            for (int slotId : line.config.inputSlots) {
                if (input.getKey().equals(containerModule.getSlotItemId(slotId))) {
                    total += containerModule.getSlotItemCount(slotId);
                }
            }
            if (total < input.getValue())
                return false;
        }

        // 检查输出槽是否有足够空间
        for (Map.Entry<String, Integer> output : recipe.getOutput().entrySet()) {
            int remainingOutput = output.getValue();
            for (int slotId : line.config.outputSlots) {
                String slotItemId = containerModule.getSlotItemId(slotId);
                if (slotItemId == null || output.getKey().equals(slotItemId)) {
                    int space = containerModule.getSlotMaxCount(slotId) - containerModule.getSlotItemCount(slotId);
                    remainingOutput -= space;
                    if (remainingOutput <= 0)
                        break;
                }
            }
            if (remainingOutput > 0)
                return false;
        }

        // 检查蒸汽是否足够
        var comsumeSteamobj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamObj");
        var comsumeSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamEntry");
        var comsumeSteamValue = recipe.getSteam();
        if (comsumeSteamobj != null && comsumeSteamEntry != null) {
            try {
                Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                Objective objective = scoreboard.getObjective(comsumeSteamobj);
                if (objective != null) {
                    Score score = objective.getScore(comsumeSteamEntry);
                    if (score.getScore() < comsumeSteamValue) {
                        return false;
                    }
                } else {
                    FrostCraftBU.i().getLogger().warning("记分板目标 '" + comsumeSteamobj + "' 不存在！请在游戏中创建它。");
                }
            } catch (Exception e) {
                FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + comsumeSteamobj + "): ", e);
            }
        }

        return true;
    }

    @Override
    public BaseModuleSaveData getSaveData() {
        List<ProductionModuleSaveData.ProductionLineStateSaveData> saveStates = new ArrayList<>();
        for (ProductionLine line : productionLines) {
            saveStates.add(new ProductionModuleSaveData.ProductionLineStateSaveData(line.state));
        }
        return new ProductionModuleSaveData(saveStates);
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ProductionModuleSaveData) {
            ProductionModuleSaveData saveData = (ProductionModuleSaveData) data;
            List<ProductionModuleSaveData.ProductionLineStateSaveData> states = saveData.getProductionLineStates();

            // 确保状态数量与产线数量匹配
            for (int i = 0; i < Math.min(states.size(), productionLines.size()); i++) {
                ProductionModuleSaveData.ProductionLineStateSaveData stateSaveData = states.get(i);
                ProductionLine line = productionLines.get(i);
                line.state.currentRecipe = stateSaveData.currentRecipe;
                line.state.progress = stateSaveData.progress;
            }
        }
    }

    @Override
    public void onUnload() {
    }

    @Override
    public String getModuleTypeId() {
        return ModuleManager.ModuleType.ProductionModule.getName();
    }

    @Override
    public List<String> getAvailableRecipes(int lineIndex) {
        return productionLines.get(lineIndex).config.availableRecipes;
    }

    @Override
    public int getLineCount() {
        return productionLines.size();
    }

}
