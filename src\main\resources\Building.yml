小工地:
  size:
    - 5
    - 5
    - 5
  module:
    construction:
      ui: 1
  icon: 小工地
  consume:
  consSiteType: null
中工地:
  size:
    - 7
    - 7
    - 7
  module:
    construction:
      ui: 1
  icon: 中工地
  consume:
  consSiteType: null
大工地:
  size:
    - 9
    - 9
    - 9
  module:
    construction:
      ui: 1
  icon: 大工地
  consume:
  consSiteType: null

热场测试:
  icon: 热场测试
  consSiteType: 小工地
  consume:
    木材: 10
    钢材: 10
  module:
    scoreboard_aura:
      range: 10.0 # 效果范围 (半径)
      objective: "heat" # 记分项名称
      value: 100 # 要设置的值

中热场测试:
  icon: 中热场测试
  consSiteType: 中工地
  consume:
    木材: 10
    钢材: 10
  module:
    scoreboard_aura:
      range: 10.0 # 效果范围 (半径)
      objective: "heat" # 记分项名称
      value: 100 # 要设置的值

容器测试:
  icon: 容器测试
  consSiteType: 小工地
  consume:
    木材: 10
    钢材: 10
  module:
    container:
      slots:
        - slotId: 1
          maxCount: 64
          tags:
            - input
        - slotId: 2
          maxCount: 640
          tags:
            - output
        - slotId: 3
          maxCount: 64
          tags:
            - canModify

熔炉:
  icon: 熔炉
  consSiteType: 小工地
  consume:
    石头: 10
    铁锭: 5
  module:
    container:
      slots:
        - slotId: 1
          maxCount: 64
          tags: [input]
        - slotId: 2
          maxCount: 64
          tags: [input]
        - slotId: 3
          maxCount: 64
          tags: [output]
        - slotId: 4
          maxCount: 64
          tags: [input]
        - slotId: 5
          maxCount: 64
          tags: [input]
        - slotId: 6
          maxCount: 64
          tags: [output]
    production:
      lines:
        - input: [1, 2]
          output: [3]
          recipe: [铁锭生产, 钢锭生产]
        - input: [4]
          output: [5, 6]
          recipe: [钢锭生产]

资源采集站:
  icon: 资源采集站
  consSiteType: 小工地
  consume:
    木材: 5
    石头: 10
  module:
    container:
      slots:
        - slotId: 1
          maxCount: 64
          tags: [output]
        - slotId: 2
          maxCount: 64
          tags: [output]
        - slotId: 3
          maxCount: 64
          tags: [output]
    resource_gathering:
      lines:
        - output: [1]
          recipe: [木材砍伐]
        - output: [2, 3]
          recipe: [木材砍伐, 煤炭挖掘]

研究所:
  icon: 研究所
  consSiteType: 小工地
  consume:
    木材: 20
    石头: 15
    铁锭: 5
  module:
    container:
      slots:
        - slotId: 1
          maxCount: 64
          tags: [input]
        - slotId: 2
          maxCount: 64
          tags: [input]
    research:
      ui: 1
      # 研究模块不需要特殊配置，使用默认值

资源消耗站:
  icon: 资源消耗站
  consSiteType: 小工地
  consume:
    木材: 5
    石头: 5
  module:
    container:
      slots:
        - slotId: 1
          maxCount: 64
          tags: [input]
        - slotId: 2
          maxCount: 64
          tags: [input]
        - slotId: 3
          maxCount: 64
          tags: [input]
    resource_consumption:
      lines:
        - input: [1]
          recipe: [烧煤]
        - input: [2, 3]
          recipe: [烧煤, 烧煤Lv2]
