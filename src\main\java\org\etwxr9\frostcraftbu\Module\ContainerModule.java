package org.etwxr9.frostcraftbu.Module;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Entity;
import org.bukkit.entity.ItemFrame;
import org.bukkit.entity.TextDisplay;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.Behavior.IContainer;
import org.etwxr9.frostcraftbu.Module.Behavior.IItemFrame;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import net.kyori.adventure.text.Component;

/**
 * yml配置：
 * slots:
 * -
 * slotId: 1
 * maxCount: 64
 * tags:
 * - tag1
 * - tag2
 */
public class ContainerModule extends BaseModule implements IContainer, IItemFrame {

    public ContainerModule(FCBuilding building) {
        super(building);
    }

    public static class ContainerUnit {
        private int slotId;
        private String itemId;
        private int count;
        private String itemFrameUUID;
        private String textDisplayUUID; // 新增字段存储TextDisplay的UUID
        @JsonSerialize(using = org.etwxr9.frostcraftbu.Serialization.LocationSerializer.class)
        @JsonDeserialize(using = org.etwxr9.frostcraftbu.Serialization.LocationDeserializer.class)
        private Location itemFrameLocation;

        public ContainerUnit() {
        }

        public ContainerUnit(int slotId, String itemId, int count, String itemFrameUUID, Location itemFrameLocation) {
            this.slotId = slotId;
            this.itemId = itemId;
            this.count = count;
            this.itemFrameUUID = itemFrameUUID;
            this.itemFrameLocation = itemFrameLocation;
            this.textDisplayUUID = null;
        }

        public int getSlotId() {
            return slotId;
        }

        public void setSlotId(int slotId) {
            this.slotId = slotId;
        }

        public String getItemId() {
            return itemId;
        }

        public void setItemId(String itemId) {
            this.itemId = itemId;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public String getItemFrameUUID() {
            return itemFrameUUID;
        }

        public void setItemFrameUUID(String itemFrameUUID) {
            this.itemFrameUUID = itemFrameUUID;
        }

        public Location getItemFrameLocation() {
            return itemFrameLocation;
        }

        public void setItemFrameLocation(Location itemFrameLocation) {
            this.itemFrameLocation = itemFrameLocation;
        }

        public String getTextDisplayUUID() {
            return textDisplayUUID;
        }

        public void setTextDisplayUUID(String textDisplayUUID) {
            this.textDisplayUUID = textDisplayUUID;
        }
    }

    private HashMap<Integer, ContainerUnit> containerUnits = new HashMap<>();

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ContainerModuleSaveData extends BaseModuleSaveData {
        private ArrayList<ContainerUnit> containerUnits;

        public ContainerModuleSaveData() {
        }

        public ContainerModuleSaveData(ArrayList<ContainerUnit> containerUnits) {
            this.containerUnits = containerUnits;
        }

        public ArrayList<ContainerUnit> getContainerUnits() {
            return containerUnits;
        }

        @Override
        public String getModuleTypeId() {
            return ModuleManager.ModuleType.ContainerModule.getName();
        }

    }

    @Override
    public String getItemFrameUUID(int slotId) {
        return containerUnits.get(slotId).getItemFrameUUID();
    }

    @Override
    public void setItemFrameUUID(int slotId, String uuid) {
        containerUnits.get(slotId).setItemFrameUUID(uuid);
    }

    @Override
    public Location getItemFrameLocation(int slotId) {
        return containerUnits.get(slotId).getItemFrameLocation();
    }

    @Override
    public int getSlotCount() {
        return containerUnits.size();
    }

    @Override
    public String getSlotItemId(int slotIndex) {
        if (containerUnits.get(slotIndex) == null) {
            return null;
        }
        return containerUnits.get(slotIndex).getItemId();
    }

    @Override
    public int getSlotItemCount(int slotIndex) {
        if (containerUnits.get(slotIndex) == null) {
            return 0;
        }
        return containerUnits.get(slotIndex).getCount();
    }

    @Override
    public List<String> getSlotTags(int slotIndex) {
        List<Map> slots = (List<Map>) getModuleConfig().getList("slots");
        for (Map slot : slots) {
            if (((Integer) slot.get("slotId")).equals(slotIndex)) {
                return (List<String>) slot.get("tags");
            }
        }
        return null;
    }

    @Override
    public List<Integer> getSlotsByTag(String tag) {
        List<Map> slots = (List<Map>) getModuleConfig().getList("slots");
        List<Integer> matchingSlots = new ArrayList<>();
        for (Map slot : slots) {
            List<String> tags = (List<String>) slot.get("tags");
            if (tags.contains(tag)) {
                matchingSlots.add((Integer) slot.get("slotId"));
            }
        }
        return matchingSlots;
    }

    @Override
    public boolean hasSlotTag(int slotIndex, String tag) {
        List<String> tags = getSlotTags(slotIndex);
        return tags != null && tags.contains(tag);
    }

    @Override
    public int getSlotMaxCount(int slotIndex) {
        List<Map> slots = (List<Map>) getModuleConfig().getList("slots");
        for (Map slot : slots) {
            if (((Integer) slot.get("slotId")).equals(slotIndex)) {
                return (Integer) slot.get("maxCount");
            }
        }
        return 64;
    }

    /**
     * 在指定位置掉落物品
     * 
     * @param location 掉落位置
     * @param itemId   物品ID
     * @param count    物品数量
     */
    private void dropItem(Location location, String itemId, int count) {
        if (count <= 0)
            return;

        ItemStack dropItem = ItemManager.getItem(itemId);
        if (dropItem == null)
            return;

        dropItem = dropItem.clone();
        dropItem.setAmount(count);
        location.getWorld().dropItem(location, dropItem);
    }

    @Override
    public void setSlotItem(int slotIndex, String itemId, int count) {
        ContainerUnit unit = containerUnits.get(slotIndex);
        String oldItemId = unit.getItemId();
        int oldCount = unit.getCount();

        // 获取物品展示框实体
        Entity itemFrameEntity = Bukkit.getEntity(UUID.fromString(unit.getItemFrameUUID()));
        if (itemFrameEntity == null)
            return;

        // 计算掉落位置（在展示框前方）
        Location dropLoc = itemFrameEntity.getLocation().clone()
                .add(itemFrameEntity.getFacing().getDirection().multiply(0.5));

        // 情况1: 更换物品类型
        if (oldItemId != null && !oldItemId.equals(itemId)) {
            // 掉落旧物品
            if (oldCount > 0) {
                dropItem(dropLoc, oldItemId, oldCount);
            }

            // 设置新物品
            ((ItemFrame) itemFrameEntity).setItem(ItemManager.getItem(itemId));
            unit.setItemId(itemId);
            unit.setCount(count);
        }
        // 情况2: 槽位为空，添加新物品
        else if (oldItemId == null) {
            ((ItemFrame) itemFrameEntity).setItem(ItemManager.getItem(itemId));
            unit.setItemId(itemId);
            unit.setCount(count);
        }
        // 情况3: 相同物品，数量变化
        else if (oldItemId.equals(itemId)) {
            // 如果数量减少，掉落多余的物品
            if (oldCount > count) {
                dropItem(dropLoc, itemId, oldCount - count);
            }
            unit.setCount(count);
        }

        // 更新TextDisplay
        updateTextDisplay(slotIndex);
    }

    @Override
    public void cosumeItem(int slotIndex, int count) {
        ContainerUnit unit = containerUnits.get(slotIndex);
        if (unit == null)
            return;
        unit.setCount(unit.getCount() - count);
        updateTextDisplay(slotIndex);
    }

    @Override
    public void produceItem(int slotIndex, int count) {
        ContainerUnit unit = containerUnits.get(slotIndex);
        if (unit == null)
            return;
        unit.setCount(unit.getCount() + count);
        updateTextDisplay(slotIndex);
    }

    @Override
    public void clearSlot(int slotIndex) {
        ContainerUnit unit = containerUnits.get(slotIndex);

        var itemFrameEntity = Bukkit.getEntity(UUID.fromString(unit.getItemFrameUUID()));
        if (itemFrameEntity == null)
            return;

        if (unit.getItemId() != null) {
            var oldItem = ItemManager.getItem(unit.getItemId());
            var oldCount = unit.getCount();
            // drop old item
            if (oldItem != null && oldCount > 0) {
                // 在展示框前方生成物品
                Location dropLoc = itemFrameEntity.getLocation().clone()
                        .add(itemFrameEntity.getFacing().getDirection().multiply(0.5));
                ItemStack dropItem = oldItem.clone();
                dropItem.setAmount(oldCount);
                itemFrameEntity.getWorld().dropItem(dropLoc, dropItem);
            }
        }
        ((ItemFrame) itemFrameEntity).setItem(ItemStack.of(Material.AIR));
        unit.setItemId(null);
        unit.setCount(0);

        // 更新TextDisplay
        updateTextDisplay(slotIndex);
    }

    @Override
    public BaseModuleSaveData getSaveData() {
        ArrayList<ContainerUnit> unitsList = new ArrayList<>();
        // Convert HashMap to ArrayList, sorting by slotId
        containerUnits.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> unitsList.add(entry.getValue()));
        return new ContainerModuleSaveData(unitsList);
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ContainerModuleSaveData) {
            containerUnits.clear();
            ContainerModuleSaveData saveData = (ContainerModuleSaveData) data;
            for (ContainerUnit unit : saveData.getContainerUnits()) {
                containerUnits.put(unit.getSlotId(), unit);
            }
        }
    }

    @Override
    public void onLoad(boolean frist) {
        if (!frist) {
            return;
        }
        var minLoc = building.getMinLocation();
        var maxLoc = building.getMaxLocation();
        // 获取球形范围内的物品展示框实体
        double radius = Math.max(maxLoc.distance(minLoc) / 2, 1);
        var nearbyEntities = minLoc.getWorld().getNearbyEntities(
                minLoc.clone().add(maxLoc).multiply(0.5), radius, radius, radius,
                entity -> entity instanceof ItemFrame);

        // 对实体进行矩形判断
        for (Entity entity : nearbyEntities) {
            Location entityLoc = entity.getLocation();
            if (entityLoc.getX() >= minLoc.getX() && entityLoc.getX() <= maxLoc.getX() &&
                    entityLoc.getY() >= minLoc.getY() && entityLoc.getY() <= maxLoc.getY() &&
                    entityLoc.getZ() >= minLoc.getZ() && entityLoc.getZ() <= maxLoc.getZ()) {

                ItemFrame itemFrame = (ItemFrame) entity;
                PersistentDataContainer container = itemFrame.getPersistentDataContainer();
                NamespacedKey key = new org.bukkit.NamespacedKey(org.etwxr9.frostcraftbu.FrostCraftBU.i(),
                        "slotId");

                if (container.has(key, PersistentDataType.INTEGER)) {
                    int slotId = container.get(key, PersistentDataType.INTEGER);
                    ContainerUnit unit = new ContainerUnit(slotId, null, 0, itemFrame.getUniqueId().toString(),
                            itemFrame.getLocation());
                    containerUnits.put(slotId, unit);
                    // 隐藏物品展示框的名字，防止遮挡TextDisplay
                    itemFrame.setCustomNameVisible(false);
                    // 创建TextDisplay
                    createTextDisplay(slotId, itemFrame);
                }
            }
        }

        // 更新所有TextDisplay
        updateAllTextDisplays();
    }

    @Override
    public void onUnload() {
        HashMap<Location, ItemStack> itemsToDrop = new HashMap<>();

        // 收集所有需要掉落的物品信息
        for (ContainerUnit unit : containerUnits.values()) {
            if (unit.getItemId() != null && unit.getCount() > 0) {
                ItemStack itemStack = ItemManager.getItem(unit.getItemId());
                if (itemStack != null) {
                    itemStack.setAmount(unit.getCount());
                    Location dropLocation = unit.getItemFrameLocation().clone().add(0, -0.5, 0);
                    itemsToDrop.put(dropLocation, itemStack);
                }
            }
        }

        // 延迟1tick后掉落物品
        Bukkit.getScheduler().runTaskLater(org.etwxr9.frostcraftbu.FrostCraftBU.i(), () -> {
            for (Location dropLocation : itemsToDrop.keySet()) {
                dropLocation.getWorld().dropItem(dropLocation, itemsToDrop.get(dropLocation));
            }
        }, 1L);
    }

    @Override
    public String getModuleTypeId() {
        return ModuleManager.ModuleType.ContainerModule.getName();
    }

    /**
     * 更新所有容器槽位的TextDisplay显示
     * 显示物品名称和数量
     */
    public void updateAllTextDisplays() {
        for (ContainerUnit unit : containerUnits.values()) {
            updateTextDisplay(unit.getSlotId());
        }
    }

    /**
     * 更新指定槽位的TextDisplay显示
     * 
     * @param slotIndex 槽位ID
     */
    public void updateTextDisplay(int slotIndex) {
        ContainerUnit unit = containerUnits.get(slotIndex);
        if (unit == null)
            return;

        // 获取TextDisplay实体
        Entity textDisplayEntity = null;
        if (unit.getTextDisplayUUID() != null) {
            textDisplayEntity = Bukkit.getEntity(UUID.fromString(unit.getTextDisplayUUID()));
        }

        // 如果TextDisplay不存在，则返回
        if (textDisplayEntity == null)
            return;

        // 获取物品信息
        String itemId = unit.getItemId();
        int count = unit.getCount();

        // 设置显示文本
        Component displayText = Component.text("空");
        if (itemId != null) {
            ItemStack itemStack = ItemManager.getItem(itemId);
            if (itemStack != null) {
                displayText = itemStack.displayName().append(Component.text(" x" + count));
            }
        }

        // 更新TextDisplay的文本
        if (textDisplayEntity instanceof TextDisplay) {
            ((TextDisplay) textDisplayEntity).text(displayText);
        }
    }

    /**
     * 为指定槽位创建TextDisplay
     * 
     * @param slotId    槽位ID
     * @param itemFrame 物品展示框实体
     */
    private void createTextDisplay(int slotId, ItemFrame itemFrame) {
        ContainerUnit unit = containerUnits.get(slotId);
        if (unit == null)
            return;

        // 在展示框上方0.5格创建TextDisplay
        Location textDisplayLoc = itemFrame.getLocation().clone().add(0, 0.5, 0);

        // 创建TextDisplay实体
        org.bukkit.entity.TextDisplay textDisplay = (org.bukkit.entity.TextDisplay) itemFrame.getWorld()
                .spawnEntity(textDisplayLoc, org.bukkit.entity.EntityType.TEXT_DISPLAY);

        // 设置TextDisplay属性
        textDisplay.setBillboard(org.bukkit.entity.Display.Billboard.CENTER);
        textDisplay.setBackgroundColor(org.bukkit.Color.fromARGB(0, 0, 0, 0)); // 透明背景
        textDisplay.text(net.kyori.adventure.text.Component.text("空"));
        textDisplay.setViewRange(16.0f); // 可见范围16格
        textDisplay.setSeeThrough(false); // 可以透过看到

        // 保存TextDisplay的UUID
        unit.setTextDisplayUUID(textDisplay.getUniqueId().toString());
    }
}
