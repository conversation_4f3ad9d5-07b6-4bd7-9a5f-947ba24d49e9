package org.etwxr9.frostcraftbu.Module;

import org.etwxr9.frostcraftbu.Module.ConstructionModule.ConstructionModuleSaveData;
import org.etwxr9.frostcraftbu.Module.ResearchModule.ResearchModuleSaveData;
import org.etwxr9.frostcraftbu.Module.ScoreboardAuraModule.ScoreboardAuraModuleSaveData;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "@type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = ConstructionModuleSaveData.class, name = "construction"),
        @JsonSubTypes.Type(value = ScoreboardAuraModuleSaveData.class, name = "scoreboard_aura"),
        @JsonSubTypes.Type(value = ResearchModuleSaveData.class, name = "research"),
        @JsonSubTypes.Type(value = ContainerModule.ContainerModuleSaveData.class, name = "container"),
        @JsonSubTypes.Type(value = ProductionModule.ProductionModuleSaveData.class, name = "production"),
        @JsonSubTypes.Type(value = ResourceGatheringModule.ResourceGatheringModuleSaveData.class, name = "resource_gathering"),
        @JsonSubTypes.Type(value = ResourceConsumptionModule.ResourceConsumptionModuleSaveData.class, name = "resource_consumption"),
    })
public abstract class BaseModuleSaveData {

    public BaseModuleSaveData() {
    }

    public abstract String getModuleTypeId();

}
