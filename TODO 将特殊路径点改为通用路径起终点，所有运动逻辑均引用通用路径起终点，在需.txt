TODO 将特殊路径点改为通用路径起终点，所有运动逻辑均引用通用路径起终点，在需要更换路径时才重设通用路径起终点

/**
 * 无人机管理器
 * 
 * 静态值：
 * 平滑类型 SmoothingType CATMULL_ROM
 * 分辨率 Resolution 5
 * 
 * 持久储存无人机数据
 * DroneData {
 * String uuid;
 * String worldName;
 * double x, y, z;
 * }
 * droneDataMap: UUID:DroneData
 * 
 * loadDroneData
 * saveDroneData
 * updateDroneData(uuid)
 * 
 * 无人机实例缓存
 * dronecacheMap: UUID:DisplayEntity
 * 
 * getDrone(String uuid)
 * 
 * 配置:
 * 路径平滑参数（也是无人机速度乘数）PathSmooth
 * 无人机参数记分板Obj DroneObj
 * 无人机速度Entry(米/秒) DroneSpeedEntry
 * 无人机容量上限Entry DroneCapacityEntry
 * 无人机蒸汽上限Entry DroneSteamEntry
 * 无人机消耗蒸汽速度Entry DroneConsumeSteamEntry
 * 装卸货时间（tick） LoadUnloadTime
 * 充电时间（tick） ChargeTime
 * 货物偏移向量 DroneItemOffset
 * 
 * 
 * 无人机物品PDC储存
 * 起始建筑uuid startBuildingUUID
 * 目标建筑uuid endBuildingUUID
 * 
 * 无人机实体PDC储存
 * 起始建筑uuid startBuildingUUID
 * 目标建筑uuid endBuildingUUID
 * 起始展示框index startFrameIndex
 * 目的展示框index endFrameIndex
 * 当前路径起点 pathStart
 * 当前路径终点 pathEnd
 * 运行阶段 runningStage(-1-初始化到起始建筑 0-起始展示框到起始建筑 1-起始建筑到目标建筑 2-目标建筑到目标展示框
 * 3-目标展示框到目标建筑
 * 4-目标建筑到起始建筑 5-起始建筑到起始展示框)
 * 充电阶段 chargeStage(0-不需充电 1-前往充电桩建筑 2-从建筑到充电桩 3-从充电桩返回建筑)
 * 状态 status(0-空闲 1-常规运行中 2-装卸货 3-充电中 4-找不到路径 5-路径阻塞)
 * 路径index pathIndex
 * 蒸汽消耗计数 steamConsumeCount
 * 蒸汽值 steam
 * 货物实体UUID itemUUID
 * 
 * 货物display PDC储存
 * 货物itemID itemID
 * 货物数量 count
 * 
 * tick()执行无人机逻辑：
 * 1. 检查是否处于空闲、装卸货、充电中、找不到路径、路径阻塞状态，并执行对应逻辑。装卸货、充电完成时，重新设定充电阶段及运行阶段，返回
 * 2. 检查是否处于非0的充电阶段，如果是则执行对应逻辑，返回
 * 3. 检查是否耗尽蒸汽，如果耗尽，则进入充电阶段1，遍历建筑，寻找最近的充电桩，设定特殊路径起终点，返回
 * 4. 检查目标建筑或路径是否存在，如果不存在则检查genPathMap，没有则开始生成路径，路径不通则进入找不到路径状态，公屏播报，返回
 * 5. 检查是否到达路径终点，如果到达则进入下一阶段，检查当前完成的路径是否是临时路径并删除。全部结束则启动装卸货、充电中逻辑，返回
 * 6. 如果下一个路径点是实体方块则进入路径阻塞状态，删除当前路径，公屏播报
 * 7. 向下一个路径点移动，并增加蒸汽消耗计数，如果计数达到一分钟则消耗蒸汽，返回
 * 
 * 
 * 具体的充电逻辑：
 * 
 * 当进入充电阶段1时，根据特殊路径起终点确定路径并移动到下一个路径点。
 * 到达目的地时，检索建筑充电桩，进入充电阶段2，重新设定特殊路径。
 * 到达目的地时，确认建筑充电桩，进入充电阶段3，进入充电中状态
 * 如果运行阶段处于0、1、2、3阶段，则充电完成后设定运行阶段为1，前往目标建筑处，
 * 如果处于4、5阶段，则设定运行阶段为4，充电完成后前往起始建筑处。
 * 
 * 装卸货逻辑：当无人机完成4运行阶段到达起始建筑时，检查起始建筑和目标建筑的container模块的output、input展示框，
 * 根据物品类型和数量，匹配可运输的两个展示框，并设定无人机的起终展示框index，接着进入运行阶段5
 * 如果没有可运输物品，进入状态0（空闲）。
 * 完成运行阶段5时，此时无人机到达起始展示框，将展示框中的物品放入无人机（即修改数量，生成物品display）进入运行阶段0
 * 完成运行阶段2时，此时无人机到达目标展示框，卸载无人机中的物品（即修改数量，删除物品display）进入运行阶段3
 * 
 * 
 * pathMap: pathKey:DronePathResult
 * pathKey: 一个有序复合键类但支持无序匹配，储存两个位置，重写equal保证无序匹配。
 * 临时路径flag，用于在无人机完成路径后判断是否删除。
 * 正逆判断，判断给定起始点是正向还是逆向
 * 
 * addPath、removePath 向pathMap中加入或移除路径
 * 
 * generatingPathMap: 一个记录当前正在生成的路径的map，key为pathKey，value为路径生成的CompletableFuture
 * 
 * genPath: 异步函数，生成路径，开始执行前，加入generatingPathMap，完成后移除并加入pathMap。
 * 如果传入两个建筑uuid，则生成之间的路径。如果传入一个建筑uuid，则生成从建筑展示框、充电桩到建筑门口的路径。
 * 
 * removeBuildingPath: 根据建筑的位置删除所有一端为该位置的路径(这就同时包括了建筑到建筑和展示框到建筑)
 * 
 * initDrone: 初始化无人机实体，传入参数：起终点建筑uuid 当前位置,生成无人机实体，设定PDC，加入无人机列表，进入运行阶段-1。
 * 
 * getDroneItemUI: 返回无人机物品UI，展示：操作说明、设定起始建筑按钮、设定目标建筑按钮、部署按钮
 * 
 * getDroneUI: 返回无人机实体UI，展示：起始建筑、目标建筑、承载物品（可拿走）、剩余蒸汽、拿起无人机
 * 
 * removeDrone: 玩家拿起无人机时调用，删除无人机实体，从无人机列表中移除，生成无人机物品，掉落在原地，如果无人机有货物，掉落在原地
 */



 需要移动的阶段

起始运行
普通循环
充电运行
