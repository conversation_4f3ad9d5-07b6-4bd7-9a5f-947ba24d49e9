package org.etwxr9.frostcraftbu.Module;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import org.etwxr9.frostcraftbu.Building.FCBuilding;

public class ModuleManager {

    public enum ModuleType {
        BaseModule("base"),
        ContainerModule("container"),
        ConstructionModule("construction"),
        ProductionModule("production"),
        ResourceGatheringModule("resource_gathering"),
        ResourceConsumptionModule("resource_consumption"),
        ScoreboardAuraModule("scoreboard_aura"),
        ResearchModule("research"),
        ChargeModule("charge");

        private final String name;

        ModuleType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    // 注册表：存储模块类型名称到模块实例工厂的映射
    // Supplier<BaseModule> 是一个函数式接口，代表一个不接受参数但返回 BaseModule 实例的函数
    private static final Map<String, Function<FCBuilding, BaseModule>> moduleRegistry = new HashMap<>();

    public static void registerModule() {
        moduleRegistry.put(ModuleType.ConstructionModule.getName(), ConstructionModule::new);
        moduleRegistry.put(ModuleType.ScoreboardAuraModule.getName(), ScoreboardAuraModule::new);
        moduleRegistry.put(ModuleType.ResearchModule.getName(), ResearchModule::new);
        moduleRegistry.put(ModuleType.ContainerModule.getName(), ContainerModule::new);
        moduleRegistry.put(ModuleType.ProductionModule.getName(), ProductionModule::new);
        moduleRegistry.put(ModuleType.ResourceGatheringModule.getName(), ResourceGatheringModule::new);
        moduleRegistry.put(ModuleType.ResourceConsumptionModule.getName(), ResourceConsumptionModule::new);
    }

    /**
     * 根据模块ID创建模块实例
     * 
     * @param moduleId 模块类型ID
     * @param building 所属建筑实例
     * @return 创建的模块实例，如果创建失败返回null
     */
    public static BaseModule createModule(String moduleId, FCBuilding building, boolean frist) {
        if (moduleId == null || moduleId.isEmpty()) {
            System.err.println("Error: Module ID cannot be null or empty");
            return null;
        }

        if (building == null) {
            System.err.println("Error: Building cannot be null");
            return null;
        }

        // 从注册表中获取模块工厂
        var cons = moduleRegistry.get(moduleId);
        if (cons == null) {
            System.err.println("Error: No module registered with ID: " + moduleId);
            return null;
        }

        try {
            // 创建模块实例
            BaseModule module = cons.apply(building);
            // 调用 onLoad 进行初始化
            module.onLoad(frist);
            return module;
        } catch (Exception e) {
            System.err.println("Error creating module " + moduleId + ": " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    // 可选：添加一个方法来获取已注册的类型，用于调试或管理
    public static java.util.Set<String> getRegisteredModuleTypes() {
        return moduleRegistry.keySet();
    }
}
