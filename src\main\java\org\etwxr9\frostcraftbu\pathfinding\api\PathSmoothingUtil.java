package org.etwxr9.frostcraftbu.pathfinding.api;

import org.bukkit.Location;
import java.util.ArrayList;
import java.util.List;

/**
 * 路径平滑工具类
 * 
 * 提供多种路径平滑算法，将原始的寻路节点转换为更自然的飞行路径
 */
public class PathSmoothingUtil {
    
    /**
     * 平滑算法类型
     */
    public enum SmoothingType {
        LINEAR,         // 线性插值
        CATMULL_ROM,    // Catmull-Rom样条曲线
        BEZIER          // 贝塞尔曲线（简化版）
    }
    
    /**
     * 3D向量类，用于数学计算
     */
    public static class Vector3D {
        public final double x, y, z;
        
        public Vector3D(double x, double y, double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        public Vector3D(Location location) {
            this.x = location.getX();
            this.y = location.getY();
            this.z = location.getZ();
        }
        
        public Vector3D add(Vector3D other) {
            return new Vector3D(x + other.x, y + other.y, z + other.z);
        }
        
        public Vector3D subtract(Vector3D other) {
            return new Vector3D(x - other.x, y - other.y, z - other.z);
        }
        
        public Vector3D multiply(double scalar) {
            return new Vector3D(x * scalar, y * scalar, z * scalar);
        }
        
        public double length() {
            return Math.sqrt(x * x + y * y + z * z);
        }
        
        public Vector3D normalize() {
            double len = length();
            if (len == 0) return new Vector3D(0, 0, 0);
            return new Vector3D(x / len, y / len, z / len);
        }
        
        public Location toLocation(Location template) {
            return new Location(template.getWorld(), x, y, z);
        }
    }
    
    /**
     * 对路径进行平滑处理
     * 
     * @param rawPath 原始路径点列表
     * @param smoothingType 平滑算法类型
     * @param resolution 平滑分辨率（每段的插值点数）
     * @return 平滑后的路径点列表
     */
    public static List<Location> smoothPath(List<Location> rawPath, SmoothingType smoothingType, int resolution) {
        if (rawPath == null || rawPath.size() < 2) {
            return rawPath != null ? new ArrayList<>(rawPath) : new ArrayList<>();
        }
        
        switch (smoothingType) {
            case LINEAR:
                return linearSmoothing(rawPath, resolution);
            case CATMULL_ROM:
                return catmullRomSmoothing(rawPath, resolution);
            case BEZIER:
                return bezierSmoothing(rawPath, resolution);
            default:
                return new ArrayList<>(rawPath);
        }
    }
    
    /**
     * 线性插值平滑
     */
    private static List<Location> linearSmoothing(List<Location> rawPath, int resolution) {
        List<Location> smoothedPath = new ArrayList<>();
        
        for (int i = 0; i < rawPath.size() - 1; i++) {
            Location current = rawPath.get(i);
            Location next = rawPath.get(i + 1);
            
            // 添加当前点
            smoothedPath.add(current.clone());
            
            // 在当前点和下一点之间插值
            for (int j = 1; j < resolution; j++) {
                double t = (double) j / resolution;
                double x = current.getX() + (next.getX() - current.getX()) * t;
                double y = current.getY() + (next.getY() - current.getY()) * t;
                double z = current.getZ() + (next.getZ() - current.getZ()) * t;
                
                smoothedPath.add(new Location(current.getWorld(), x, y, z));
            }
        }
        
        // 添加最后一个点
        smoothedPath.add(rawPath.get(rawPath.size() - 1).clone());
        
        return smoothedPath;
    }
    
    /**
     * Catmull-Rom样条曲线平滑
     */
    private static List<Location> catmullRomSmoothing(List<Location> rawPath, int resolution) {
        if (rawPath.size() < 2) {
            return new ArrayList<>(rawPath);
        }
        
        // 转换为Vector3D列表
        List<Vector3D> controlPoints = new ArrayList<>();
        for (Location loc : rawPath) {
            controlPoints.add(new Vector3D(loc));
        }
        
        List<Location> smoothedPath = new ArrayList<>();
        Location template = rawPath.get(0);
        
        // 生成平滑路径
        double maxT = controlPoints.size() - 1;
        double step = maxT / (resolution * (controlPoints.size() - 1));
        
        for (double t = 0; t <= maxT; t += step) {
            Vector3D point = getCatmullRomPoint(controlPoints, t);
            smoothedPath.add(point.toLocation(template));
        }
        
        // 确保包含最后一个点
        if (!smoothedPath.isEmpty()) {
            Vector3D lastPoint = getCatmullRomPoint(controlPoints, maxT);
            smoothedPath.add(lastPoint.toLocation(template));
        }
        
        return smoothedPath;
    }
    
    /**
     * 计算Catmull-Rom样条曲线上的点
     */
    private static Vector3D getCatmullRomPoint(List<Vector3D> controlPoints, double t) {
        if (controlPoints.size() < 2) {
            return controlPoints.isEmpty() ? new Vector3D(0, 0, 0) : controlPoints.get(0);
        }
        
        // 确保t在有效范围内
        t = Math.max(0, Math.min(controlPoints.size() - 1, t));
        
        int segment = (int) Math.floor(t);
        double localT = t - segment;
        
        // 获取四个控制点
        Vector3D p0 = getCatmullRomControlPoint(controlPoints, segment - 1);
        Vector3D p1 = getCatmullRomControlPoint(controlPoints, segment);
        Vector3D p2 = getCatmullRomControlPoint(controlPoints, segment + 1);
        Vector3D p3 = getCatmullRomControlPoint(controlPoints, segment + 2);
        
        // Catmull-Rom样条公式
        double t2 = localT * localT;
        double t3 = t2 * localT;
        
        Vector3D result = p0.multiply(-0.5 * t3 + t2 - 0.5 * localT)
                .add(p1.multiply(1.5 * t3 - 2.5 * t2 + 1))
                .add(p2.multiply(-1.5 * t3 + 2 * t2 + 0.5 * localT))
                .add(p3.multiply(0.5 * t3 - 0.5 * t2));
        
        return result;
    }
    
    /**
     * 获取Catmull-Rom控制点，处理边界情况
     */
    private static Vector3D getCatmullRomControlPoint(List<Vector3D> controlPoints, int index) {
        if (index < 0) {
            // 延伸第一个点
            Vector3D p0 = controlPoints.get(0);
            Vector3D p1 = controlPoints.get(1);
            return p0.multiply(2).subtract(p1);
        } else if (index >= controlPoints.size()) {
            // 延伸最后一个点
            int lastIndex = controlPoints.size() - 1;
            Vector3D p0 = controlPoints.get(lastIndex - 1);
            Vector3D p1 = controlPoints.get(lastIndex);
            return p1.multiply(2).subtract(p0);
        } else {
            return controlPoints.get(index);
        }
    }
    
    /**
     * 简化的贝塞尔曲线平滑
     */
    private static List<Location> bezierSmoothing(List<Location> rawPath, int resolution) {
        if (rawPath.size() < 3) {
            return linearSmoothing(rawPath, resolution);
        }
        
        List<Location> smoothedPath = new ArrayList<>();
        
        // 对每三个连续点创建二次贝塞尔曲线
        for (int i = 0; i < rawPath.size() - 2; i++) {
            Location p0 = rawPath.get(i);
            Location p1 = rawPath.get(i + 1);
            Location p2 = rawPath.get(i + 2);
            
            // 生成贝塞尔曲线点
            for (int j = 0; j < resolution; j++) {
                double t = (double) j / resolution;
                double x = (1 - t) * (1 - t) * p0.getX() + 2 * (1 - t) * t * p1.getX() + t * t * p2.getX();
                double y = (1 - t) * (1 - t) * p0.getY() + 2 * (1 - t) * t * p1.getY() + t * t * p2.getY();
                double z = (1 - t) * (1 - t) * p0.getZ() + 2 * (1 - t) * t * p1.getZ() + t * t * p2.getZ();
                
                smoothedPath.add(new Location(p0.getWorld(), x, y, z));
            }
        }
        
        // 添加最后一个点
        smoothedPath.add(rawPath.get(rawPath.size() - 1).clone());
        
        return smoothedPath;
    }
    
    /**
     * 计算路径的总长度
     */
    public static double calculatePathLength(List<Location> path) {
        if (path == null || path.size() < 2) {
            return 0.0;
        }
        
        double totalLength = 0.0;
        for (int i = 0; i < path.size() - 1; i++) {
            totalLength += path.get(i).distance(path.get(i + 1));
        }
        
        return totalLength;
    }
}
