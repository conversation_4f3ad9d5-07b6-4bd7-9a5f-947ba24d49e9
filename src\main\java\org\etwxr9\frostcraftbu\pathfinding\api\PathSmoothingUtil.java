package org.etwxr9.frostcraftbu.pathfinding.api;

import org.bukkit.Location;
import java.util.ArrayList;
import java.util.List;

/**
 * 路径平滑工具类
 * 
 * 提供多种路径平滑算法，将原始的寻路节点转换为更自然的飞行路径
 */
public class PathSmoothingUtil {
    
    /**
     * 平滑算法类型
     */
    public enum SmoothingType {
        LINEAR,         // 线性插值
        CATMULL_ROM,    // Catmull-Rom样条曲线
        BEZIER          // 贝塞尔曲线（简化版）
    }
    
    /**
     * 3D向量类，用于数学计算
     */
    public static class Vector3D {
        public final double x, y, z;
        
        public Vector3D(double x, double y, double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        public Vector3D(Location location) {
            this.x = location.getX();
            this.y = location.getY();
            this.z = location.getZ();
        }
        
        public Vector3D add(Vector3D other) {
            return new Vector3D(x + other.x, y + other.y, z + other.z);
        }
        
        public Vector3D subtract(Vector3D other) {
            return new Vector3D(x - other.x, y - other.y, z - other.z);
        }
        
        public Vector3D multiply(double scalar) {
            return new Vector3D(x * scalar, y * scalar, z * scalar);
        }
        
        public double length() {
            return Math.sqrt(x * x + y * y + z * z);
        }
        
        public Vector3D normalize() {
            double len = length();
            if (len == 0) return new Vector3D(0, 0, 0);
            return new Vector3D(x / len, y / len, z / len);
        }
        
        public Location toLocation(Location template) {
            return new Location(template.getWorld(), x, y, z);
        }
    }
    
    /**
     * 对路径进行平滑处理
     * 
     * @param rawPath 原始路径点列表
     * @param smoothingType 平滑算法类型
     * @param resolution 平滑分辨率（每段的插值点数）
     * @return 平滑后的路径点列表
     */
    public static List<Location> smoothPath(List<Location> rawPath, SmoothingType smoothingType, int resolution) {
        if (rawPath == null || rawPath.size() < 2) {
            return rawPath != null ? new ArrayList<>(rawPath) : new ArrayList<>();
        }
        
        switch (smoothingType) {
            case LINEAR:
                return linearSmoothing(rawPath, resolution);
            case CATMULL_ROM:
                return catmullRomSmoothing(rawPath, resolution);
            case BEZIER:
                return bezierSmoothing(rawPath, resolution);
            default:
                return new ArrayList<>(rawPath);
        }
    }
    
    /**
     * 线性插值平滑
     */
    private static List<Location> linearSmoothing(List<Location> rawPath, int resolution) {
        List<Location> smoothedPath = new ArrayList<>();
        
        for (int i = 0; i < rawPath.size() - 1; i++) {
            Location current = rawPath.get(i);
            Location next = rawPath.get(i + 1);
            
            // 添加当前点
            smoothedPath.add(current.clone());
            
            // 在当前点和下一点之间插值
            for (int j = 1; j < resolution; j++) {
                double t = (double) j / resolution;
                double x = current.getX() + (next.getX() - current.getX()) * t;
                double y = current.getY() + (next.getY() - current.getY()) * t;
                double z = current.getZ() + (next.getZ() - current.getZ()) * t;
                
                smoothedPath.add(new Location(current.getWorld(), x, y, z));
            }
        }
        
        // 添加最后一个点
        smoothedPath.add(rawPath.get(rawPath.size() - 1).clone());
        
        return smoothedPath;
    }
    
    /**
     * Catmull-Rom样条曲线平滑
     */
    private static List<Location> catmullRomSmoothing(List<Location> rawPath, int resolution) {
        if (rawPath.size() < 2) {
            return new ArrayList<>(rawPath);
        }
        
        // 转换为Vector3D列表
        List<Vector3D> controlPoints = new ArrayList<>();
        for (Location loc : rawPath) {
            controlPoints.add(new Vector3D(loc));
        }
        
        List<Location> smoothedPath = new ArrayList<>();
        Location template = rawPath.get(0);
        
        // 生成平滑路径
        double maxT = controlPoints.size() - 1;
        double step = maxT / (resolution * (controlPoints.size() - 1));
        
        for (double t = 0; t <= maxT; t += step) {
            Vector3D point = getCatmullRomPoint(controlPoints, t);
            smoothedPath.add(point.toLocation(template));
        }
        
        // 确保包含最后一个点
        if (!smoothedPath.isEmpty()) {
            Vector3D lastPoint = getCatmullRomPoint(controlPoints, maxT);
            smoothedPath.add(lastPoint.toLocation(template));
        }
        
        return smoothedPath;
    }
    
    /**
     * 计算Catmull-Rom样条曲线上的点
     */
    private static Vector3D getCatmullRomPoint(List<Vector3D> controlPoints, double t) {
        if (controlPoints.size() < 2) {
            return controlPoints.isEmpty() ? new Vector3D(0, 0, 0) : controlPoints.get(0);
        }
        
        // 确保t在有效范围内
        t = Math.max(0, Math.min(controlPoints.size() - 1, t));
        
        int segment = (int) Math.floor(t);
        double localT = t - segment;
        
        // 获取四个控制点
        Vector3D p0 = getCatmullRomControlPoint(controlPoints, segment - 1);
        Vector3D p1 = getCatmullRomControlPoint(controlPoints, segment);
        Vector3D p2 = getCatmullRomControlPoint(controlPoints, segment + 1);
        Vector3D p3 = getCatmullRomControlPoint(controlPoints, segment + 2);
        
        // Catmull-Rom样条公式
        double t2 = localT * localT;
        double t3 = t2 * localT;
        
        Vector3D result = p0.multiply(-0.5 * t3 + t2 - 0.5 * localT)
                .add(p1.multiply(1.5 * t3 - 2.5 * t2 + 1))
                .add(p2.multiply(-1.5 * t3 + 2 * t2 + 0.5 * localT))
                .add(p3.multiply(0.5 * t3 - 0.5 * t2));
        
        return result;
    }
    
    /**
     * 获取Catmull-Rom控制点，处理边界情况
     */
    private static Vector3D getCatmullRomControlPoint(List<Vector3D> controlPoints, int index) {
        if (index < 0) {
            // 延伸第一个点
            Vector3D p0 = controlPoints.get(0);
            Vector3D p1 = controlPoints.get(1);
            return p0.multiply(2).subtract(p1);
        } else if (index >= controlPoints.size()) {
            // 延伸最后一个点
            int lastIndex = controlPoints.size() - 1;
            Vector3D p0 = controlPoints.get(lastIndex - 1);
            Vector3D p1 = controlPoints.get(lastIndex);
            return p1.multiply(2).subtract(p0);
        } else {
            return controlPoints.get(index);
        }
    }
    
    /**
     * 简化的贝塞尔曲线平滑
     */
    private static List<Location> bezierSmoothing(List<Location> rawPath, int resolution) {
        if (rawPath.size() < 3) {
            return linearSmoothing(rawPath, resolution);
        }
        
        List<Location> smoothedPath = new ArrayList<>();
        
        // 对每三个连续点创建二次贝塞尔曲线
        for (int i = 0; i < rawPath.size() - 2; i++) {
            Location p0 = rawPath.get(i);
            Location p1 = rawPath.get(i + 1);
            Location p2 = rawPath.get(i + 2);
            
            // 生成贝塞尔曲线点
            for (int j = 0; j < resolution; j++) {
                double t = (double) j / resolution;
                double x = (1 - t) * (1 - t) * p0.getX() + 2 * (1 - t) * t * p1.getX() + t * t * p2.getX();
                double y = (1 - t) * (1 - t) * p0.getY() + 2 * (1 - t) * t * p1.getY() + t * t * p2.getY();
                double z = (1 - t) * (1 - t) * p0.getZ() + 2 * (1 - t) * t * p1.getZ() + t * t * p2.getZ();
                
                smoothedPath.add(new Location(p0.getWorld(), x, y, z));
            }
        }
        
        // 添加最后一个点
        smoothedPath.add(rawPath.get(rawPath.size() - 1).clone());
        
        return smoothedPath;
    }
    
    /**
     * 计算路径的总长度
     */
    public static double calculatePathLength(List<Location> path) {
        if (path == null || path.size() < 2) {
            return 0.0;
        }

        double totalLength = 0.0;
        for (int i = 0; i < path.size() - 1; i++) {
            totalLength += path.get(i).distance(path.get(i + 1));
        }

        return totalLength;
    }

    /**
     * 实时计算无人机在路径上的平滑位置
     *
     * @param elapsedTicks 无人机已运行的tick数
     * @param speedMetersPerSecond 无人机速度（米/秒）
     * @param rawPath 原始路径点列表
     * @param smoothingType 平滑算法类型
     * @return 计算出的下一个位置，如果路径已完成则返回null
     */
    public static Location calculateRealtimePosition(int elapsedTicks, double speedMetersPerSecond,
                                                   List<Location> rawPath, SmoothingType smoothingType) {
        return calculateRealtimePosition(elapsedTicks, speedMetersPerSecond, rawPath, smoothingType, 10);
    }

    /**
     * 实时计算无人机在路径上的平滑位置（带分辨率参数）
     *
     * @param elapsedTicks 无人机已运行的tick数
     * @param speedMetersPerSecond 无人机速度（米/秒）
     * @param rawPath 原始路径点列表
     * @param smoothingType 平滑算法类型
     * @param resolution 平滑分辨率
     * @return 计算出的下一个位置，如果路径已完成则返回null
     */
    public static Location calculateRealtimePosition(int elapsedTicks, double speedMetersPerSecond,
                                                   List<Location> rawPath, SmoothingType smoothingType,
                                                   int resolution) {
        if (rawPath == null || rawPath.isEmpty()) {
            return null;
        }

        if (rawPath.size() == 1) {
            return rawPath.get(0).clone();
        }

        // 计算已经行进的距离（米）
        // 1 tick = 1/20 秒
        double elapsedSeconds = elapsedTicks / 20.0;
        double traveledDistance = elapsedSeconds * speedMetersPerSecond;

        // 根据平滑类型计算位置
        switch (smoothingType) {
            case LINEAR:
                return calculateLinearRealtimePosition(traveledDistance, rawPath);
            case CATMULL_ROM:
                return calculateCatmullRomRealtimePosition(traveledDistance, rawPath, resolution);
            case BEZIER:
                return calculateBezierRealtimePosition(traveledDistance, rawPath, resolution);
            default:
                return calculateLinearRealtimePosition(traveledDistance, rawPath);
        }
    }

    /**
     * 线性插值实时位置计算
     */
    private static Location calculateLinearRealtimePosition(double traveledDistance, List<Location> rawPath) {
        if (traveledDistance <= 0) {
            return rawPath.get(0).clone();
        }

        double accumulatedDistance = 0.0;

        // 遍历路径段，找到当前位置所在的段
        for (int i = 0; i < rawPath.size() - 1; i++) {
            Location current = rawPath.get(i);
            Location next = rawPath.get(i + 1);
            double segmentLength = current.distance(next);

            if (accumulatedDistance + segmentLength >= traveledDistance) {
                // 找到了目标段，计算在该段中的位置
                double segmentProgress = (traveledDistance - accumulatedDistance) / segmentLength;

                // 线性插值计算位置
                double x = current.getX() + (next.getX() - current.getX()) * segmentProgress;
                double y = current.getY() + (next.getY() - current.getY()) * segmentProgress;
                double z = current.getZ() + (next.getZ() - current.getZ()) * segmentProgress;

                return new Location(current.getWorld(), x, y, z);
            }

            accumulatedDistance += segmentLength;
        }

        // 如果超出了路径长度，返回终点
        return rawPath.get(rawPath.size() - 1).clone();
    }

    /**
     * Catmull-Rom样条曲线实时位置计算
     */
    private static Location calculateCatmullRomRealtimePosition(double traveledDistance, List<Location> rawPath, int resolution) {
        if (traveledDistance <= 0) {
            return rawPath.get(0).clone();
        }

        // 生成平滑路径
        List<Location> smoothedPath = smoothPath(rawPath, SmoothingType.CATMULL_ROM, resolution);

        if (smoothedPath.isEmpty()) {
            return rawPath.get(0).clone();
        }

        // 在平滑路径上找到对应位置
        return findPositionOnPath(traveledDistance, smoothedPath);
    }

    /**
     * 贝塞尔曲线实时位置计算
     */
    private static Location calculateBezierRealtimePosition(double traveledDistance, List<Location> rawPath, int resolution) {
        if (traveledDistance <= 0) {
            return rawPath.get(0).clone();
        }

        // 生成平滑路径
        List<Location> smoothedPath = smoothPath(rawPath, SmoothingType.BEZIER, resolution);

        if (smoothedPath.isEmpty()) {
            return rawPath.get(0).clone();
        }

        // 在平滑路径上找到对应位置
        return findPositionOnPath(traveledDistance, smoothedPath);
    }

    /**
     * 在给定路径上根据行进距离找到对应位置
     */
    private static Location findPositionOnPath(double traveledDistance, List<Location> path) {
        if (path.isEmpty()) {
            return null;
        }

        if (path.size() == 1) {
            return path.get(0).clone();
        }

        double accumulatedDistance = 0.0;

        // 遍历路径段，找到当前位置所在的段
        for (int i = 0; i < path.size() - 1; i++) {
            Location current = path.get(i);
            Location next = path.get(i + 1);
            double segmentLength = current.distance(next);

            if (accumulatedDistance + segmentLength >= traveledDistance) {
                // 找到了目标段，计算在该段中的位置
                double segmentProgress = (traveledDistance - accumulatedDistance) / segmentLength;

                // 线性插值计算位置
                double x = current.getX() + (next.getX() - current.getX()) * segmentProgress;
                double y = current.getY() + (next.getY() - current.getY()) * segmentProgress;
                double z = current.getZ() + (next.getZ() - current.getZ()) * segmentProgress;

                return new Location(current.getWorld(), x, y, z);
            }

            accumulatedDistance += segmentLength;
        }

        // 如果超出了路径长度，返回终点
        return path.get(path.size() - 1).clone();
    }

    /**
     * 检查无人机是否已完成路径
     *
     * @param elapsedTicks 已运行的tick数
     * @param speedMetersPerSecond 速度（米/秒）
     * @param rawPath 原始路径
     * @return 如果已完成路径返回true
     */
    public static boolean isPathCompleted(int elapsedTicks, double speedMetersPerSecond, List<Location> rawPath) {
        if (rawPath == null || rawPath.isEmpty()) {
            return true;
        }

        double elapsedSeconds = elapsedTicks / 20.0;
        double traveledDistance = elapsedSeconds * speedMetersPerSecond;
        double pathLength = calculatePathLength(rawPath);

        return traveledDistance >= pathLength;
    }

    /**
     * 计算无人机完成整个路径需要的时间（tick）
     *
     * @param speedMetersPerSecond 速度（米/秒）
     * @param rawPath 原始路径
     * @return 需要的tick数，如果路径无效返回0
     */
    public static int calculatePathDurationTicks(double speedMetersPerSecond, List<Location> rawPath) {
        if (rawPath == null || rawPath.isEmpty() || speedMetersPerSecond <= 0) {
            return 0;
        }

        double pathLength = calculatePathLength(rawPath);
        double durationSeconds = pathLength / speedMetersPerSecond;

        return (int) Math.ceil(durationSeconds * 20); // 转换为tick
    }

    /**
     * 计算无人机在路径上的进度百分比
     *
     * @param elapsedTicks 已运行的tick数
     * @param speedMetersPerSecond 速度（米/秒）
     * @param rawPath 原始路径
     * @return 进度百分比（0.0-1.0），如果路径无效返回0.0
     */
    public static double calculatePathProgress(int elapsedTicks, double speedMetersPerSecond, List<Location> rawPath) {
        if (rawPath == null || rawPath.isEmpty()) {
            return 0.0;
        }

        double elapsedSeconds = elapsedTicks / 20.0;
        double traveledDistance = elapsedSeconds * speedMetersPerSecond;
        double pathLength = calculatePathLength(rawPath);

        if (pathLength <= 0) {
            return 1.0;
        }

        return Math.min(1.0, traveledDistance / pathLength);
    }

    /**
     * 计算无人机当前的运动方向向量
     *
     * @param elapsedTicks 已运行的tick数
     * @param speedMetersPerSecond 速度（米/秒）
     * @param rawPath 原始路径
     * @param smoothingType 平滑算法类型
     * @return 方向向量，如果无法计算返回null
     */
    public static Vector3D calculateMovementDirection(int elapsedTicks, double speedMetersPerSecond,
                                                     List<Location> rawPath, SmoothingType smoothingType) {
        // 计算当前位置
        Location currentPos = calculateRealtimePosition(elapsedTicks, speedMetersPerSecond, rawPath, smoothingType);
        if (currentPos == null) {
            return null;
        }

        // 计算下一个位置（提前1 tick）
        Location nextPos = calculateRealtimePosition(elapsedTicks + 1, speedMetersPerSecond, rawPath, smoothingType);
        if (nextPos == null) {
            return null;
        }

        // 计算方向向量
        double dx = nextPos.getX() - currentPos.getX();
        double dy = nextPos.getY() - currentPos.getY();
        double dz = nextPos.getZ() - currentPos.getZ();

        return new Vector3D(dx, dy, dz).normalize();
    }
}
