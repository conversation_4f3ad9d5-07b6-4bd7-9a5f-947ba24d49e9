package org.etwxr9.frostcraftbu.pathfinding.command;

import de.bsommerfeld.pathetic.api.pathing.Pathfinder;
import de.bsommerfeld.pathetic.api.pathing.result.PathfinderResult;
import de.bsommerfeld.pathetic.api.wrapper.PathPosition;
import de.bsommerfeld.pathetic.bukkit.context.BukkitEnvironmentContext;
import de.bsommerfeld.pathetic.bukkit.mapper.BukkitMapper;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabExecutor;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.etwxr9.frostcraftbu.pathfinding.visualizer.DroneVisualizer;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletionStage;

/**
 * 无人机命令处理器，实现无人机寻路的用户界面
 * 
 * 支持的命令：
 * - /drone pos1 - 设置起点位置
 * - /drone pos2 - 设置终点位置
 * - /drone start [smooth|linear] [resolution] - 开始无人机寻路并显示动画
 *   - smooth: 使用曲线平滑（默认）
 *   - linear: 使用线性插值
 *   - resolution: 平滑分辨率，1-20（默认10）
 */
public class DroneCommand implements TabExecutor {

    // 存储玩家会话数据
    private static final Map<UUID, DroneSession> SESSION_MAP = new HashMap<>();

    private final Pathfinder pathfinder;
    private final DroneVisualizer visualizer;

    public DroneCommand(Pathfinder pathfinder, Plugin plugin) {
        this.pathfinder = pathfinder;
        this.visualizer = new DroneVisualizer(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 确保发送者是玩家
        if (!(sender instanceof Player)) {
            sender.sendMessage("此命令只能由玩家执行");
            return false;
        }

        // 确保命令有至少一个参数
        if (args.length < 1) {
            sender.sendMessage("用法: /drone <pos1|pos2|start> [smooth|linear] [resolution]");
            return false;
        }

        Player player = (Player) sender;
        
        // 获取或创建玩家会话
        DroneSession session = SESSION_MAP.computeIfAbsent(player.getUniqueId(), k -> new DroneSession());

        // 处理不同的命令
        switch (args[0].toLowerCase()) {
            case "pos1":
                handlePos1Command(player, session);
                break;
                
            case "pos2":
                handlePos2Command(player, session);
                break;
                
            case "start":
                handleStartCommand(player, session, args);
                break;

            default:
                player.sendMessage("未知命令参数: " + args[0]);
                player.sendMessage("用法: /drone <pos1|pos2|start> [smooth|linear] [resolution]");
                return false;
        }

        return true;
    }

    /**
     * 处理pos1命令 - 设置起点
     */
    private void handlePos1Command(Player player, DroneSession session) {
        Location location = player.getLocation().getBlock().getLocation().clone().add(0.5, 0.5, 0.5);
        session.setStartPosition(location);
        player.sendMessage("§a无人机起点已设置: §f" + formatLocation(location));
    }

    /**
     * 处理pos2命令 - 设置终点
     */
    private void handlePos2Command(Player player, DroneSession session) {
        Location location = player.getLocation().getBlock().getLocation().clone().add(0.5, 0.5, 0.5);
        session.setEndPosition(location);
        player.sendMessage("§a无人机终点已设置: §f" + formatLocation(location));
    }

    /**
     * 处理start命令 - 开始寻路
     */
    private void handleStartCommand(Player player, DroneSession session, String[] args) {
        // 检查是否设置了起点和终点
        if (!session.isComplete()) {
            player.sendMessage("§c请先设置起点和终点位置！");
            player.sendMessage("§e使用 /drone pos1 设置起点");
            player.sendMessage("§e使用 /drone pos2 设置终点");
            return;
        }

        // 解析动画参数
        final boolean useSmoothing;
        final int resolution;

        // 解析动画模式
        if (args.length > 1) {
            String mode = args[1].toLowerCase();
            if ("linear".equals(mode)) {
                useSmoothing = false;
            } else if ("smooth".equals(mode)) {
                useSmoothing = true;
            } else {
                player.sendMessage("§c无效的动画模式: " + args[1]);
                player.sendMessage("§e请使用 'smooth' 或 'linear'");
                return;
            }
        } else {
            useSmoothing = true; // 默认使用平滑
        }

        // 解析分辨率
        if (args.length > 2) {
            try {
                int tempResolution = Integer.parseInt(args[2]);
                if (tempResolution < 1 || tempResolution > 20) {
                    player.sendMessage("§c分辨率必须在1-20之间");
                    return;
                }
                resolution = tempResolution;
            } catch (NumberFormatException e) {
                player.sendMessage("§c无效的分辨率数值: " + args[2]);
                return;
            }
        } else {
            resolution = 10; // 默认分辨率
        }

        // 转换为PathPosition
        PathPosition start = BukkitMapper.toPathPosition(session.getStartPosition());
        PathPosition target = BukkitMapper.toPathPosition(session.getEndPosition());

        // 显示寻路信息
        double distance = start.distance(target);
        player.sendMessage("§6开始无人机寻路...");
        player.sendMessage("§7起点: §f" + formatLocation(session.getStartPosition()));
        player.sendMessage("§7终点: §f" + formatLocation(session.getEndPosition()));
        player.sendMessage("§7直线距离: §f" + String.format("%.2f", distance) + " 格");

        // 执行寻路
        CompletionStage<PathfinderResult> pathfindingResult = 
            pathfinder.findPath(start, target, new BukkitEnvironmentContext(player.getWorld()));

        // 处理寻路结果
        pathfindingResult.thenAccept(result -> {
            player.sendMessage("§6寻路完成！");
            player.sendMessage("§7状态: §f" + result.getPathState().name());
            player.sendMessage("§7路径长度: §f" + result.getPath().length() + " 个节点");

            // 如果寻路成功，启动可视化动画
            if (result.successful() || result.hasFallenBack()) {
                String modeText = useSmoothing ? "曲线平滑" : "线性插值";
                player.sendMessage("§a正在启动无人机动画... (模式: " + modeText + ", 分辨率: " + resolution + ")");

                // 启动无人机动画
                visualizer.animateDroneAlongPath(result.getPath(), player.getWorld(), player, useSmoothing, resolution);

                if (result.hasFallenBack()) {
                    player.sendMessage("§e注意: 使用了备用寻路策略");
                }
            } else {
                player.sendMessage("§c无法找到有效路径！");
                player.sendMessage("§e请检查起点和终点是否合理，或尝试调整位置");
            }
        }).exceptionally(throwable -> {
            player.sendMessage("§c寻路过程中发生错误: " + throwable.getMessage());
            throwable.printStackTrace();
            return null;
        });
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(Location location) {
        return String.format("(%.1f, %.1f, %.1f)", 
            location.getX(), location.getY(), location.getZ());
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 1) {
            return Arrays.asList("pos1", "pos2", "start");
        } else if (args.length == 2 && "start".equalsIgnoreCase(args[0])) {
            return Arrays.asList("smooth", "linear");
        } else if (args.length == 3 && "start".equalsIgnoreCase(args[0])) {
            return Arrays.asList("5", "10", "15", "20");
        }
        return Arrays.asList();
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        visualizer.cleanup();
        SESSION_MAP.clear();
    }

    /**
     * 无人机会话数据类
     */
    private static class DroneSession {
        private Location startPosition;
        private Location endPosition;

        public void setStartPosition(Location startPosition) {
            this.startPosition = startPosition;
        }

        public void setEndPosition(Location endPosition) {
            this.endPosition = endPosition;
        }

        public Location getStartPosition() {
            return startPosition;
        }

        public Location getEndPosition() {
            return endPosition;
        }

        public boolean isComplete() {
            return startPosition != null && endPosition != null;
        }
    }
}
