package org.etwxr9.frostcraftbu.Building;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collection;
import java.util.HashMap;
import java.util.Vector;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.BlockFace;
import org.bukkit.block.Sign;
import org.bukkit.block.sign.Side;
import org.bukkit.configuration.file.YamlConfiguration;
import org.etwxr9.buildingunit.BuildingUnitAPI;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.FCBuilding.FCBuildingSaveData;
import org.etwxr9.frostcraftbu.Module.BaseModule;
import org.etwxr9.frostcraftbu.Module.ModuleManager;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.databind.ObjectMapper;

public class BuildingManager {
    private static BuildingManager instance;

    private static HashMap<String, FCBuilding> buildingMap;

    public static synchronized BuildingManager i() {
        if (instance == null) {
            instance = new BuildingManager();
        }
        return instance;
    }

    private HashMap<String, BuildingConfig> buildingInfoMap;
    private YamlConfiguration buildingYml;

    public void saveBuilding() {
        var buildingJsonPath = Path.of(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/BuildingSaveData.json");
        if (buildingMap == null) {
            FrostCraftBU.i().getLogger().warning("保存时建筑数据不存在！");
            return;
        }
        var saveData = new HashMap<String, FCBuildingSaveData>();
        buildingMap.forEach((uuid, building) -> {
            saveData.put(uuid, building.getSaveData());
        });
        try {
            // 打印baseInfoMap的内容
            // FactoryMain.i().getLogger().info(baseInfoMap.toString());
            ObjectMapper mapper = new ObjectMapper();
            mapper.setVisibility(mapper.getSerializationConfig().getDefaultVisibilityChecker()
                    .withFieldVisibility(JsonAutoDetect.Visibility.ANY)
                    .withGetterVisibility(JsonAutoDetect.Visibility.NONE)
                    .withSetterVisibility(JsonAutoDetect.Visibility.NONE)
                    .withCreatorVisibility(JsonAutoDetect.Visibility.NONE)
                    .withIsGetterVisibility(JsonAutoDetect.Visibility.NONE));
            var result = mapper.writeValueAsString(saveData);
            Files.writeString(buildingJsonPath, result);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void loadBuilding() {
        buildingMap = new HashMap<>();
        var buildingJsonPath = Path.of(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/BuildingSaveData.json");
        if (!Files.exists(buildingJsonPath)) {
            FrostCraftBU.i().getLogger().info("建筑数据文件不存在，将创建新的建筑映射");
            return;
        }

        try {
            String jsonContent = Files.readString(buildingJsonPath);
            ObjectMapper mapper = new ObjectMapper();
            mapper.setVisibility(mapper.getSerializationConfig().getDefaultVisibilityChecker()
                    .withFieldVisibility(JsonAutoDetect.Visibility.ANY)
                    .withGetterVisibility(JsonAutoDetect.Visibility.NONE)
                    .withSetterVisibility(JsonAutoDetect.Visibility.NONE)
                    .withCreatorVisibility(JsonAutoDetect.Visibility.NONE)
                    .withIsGetterVisibility(JsonAutoDetect.Visibility.NONE));

            // 使用TypeReference读取复杂类型
            var saveDataMap = (HashMap<String, FCBuildingSaveData>) mapper.readValue(jsonContent,
                    mapper.getTypeFactory().constructMapType(HashMap.class, String.class, FCBuildingSaveData.class));
            saveDataMap.forEach((uuid, saveData) -> {
                var building = initBuilding(saveData.getMachineId(), uuid, false);
                building.loadSaveData(saveData);
                addBuilding(building);
            });

            FrostCraftBU.i().getLogger().info("成功加载了 " + buildingMap.size() + " 个建筑");
        } catch (IOException e) {
            FrostCraftBU.i().getLogger().severe("加载建筑数据时出错：" + e.getMessage());
            e.printStackTrace();
            buildingMap = new HashMap<>();
        }
    }

    private BuildingManager() {
        // 初始化读取建筑配置
        buildingInfoMap = new HashMap<>();
        buildingYml = YamlConfiguration
                .loadConfiguration(new File(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/Building.yml"));
        buildingYml.getKeys(false).forEach(buildingId -> {
            var section = buildingYml.getConfigurationSection(buildingId);
            var building = new BuildingConfig(section);
            buildingInfoMap.put(buildingId, building);
        });
    }

    private FCBuilding initBuilding(String buildingId, String unitUUID, boolean frist) {
        BuildingConfig buildingInfo = getBuildingConfigMap().get(buildingId);
        if (buildingInfo == null) {
            FrostCraftBU.i().getLogger()
                    .severe("Failed to create building " + buildingId + " because it is not defined in Building.yml");
            return null;
        }
        var result = new FCBuilding(buildingId, unitUUID);

        buildingInfo.getAllModuleConfig().forEach((moduleId, moduleConfig) -> {
            var module = ModuleManager.createModule(moduleId, result, frist);
            if (module != null) {
                result.addModule(moduleId, module);
            } else {
                FrostCraftBU.i().getLogger()
                        .severe("Failed to create module '" + moduleId + "' for building " + buildingId);
            }
        });
        return result;
    }

    public YamlConfiguration getBuildingYml() {
        return buildingYml;
    }

    public HashMap<String, BuildingConfig> getBuildingConfigMap() {
        return buildingInfoMap;
    }

    public BuildingConfig getBuildingConfig(String buildingId) {
        return buildingInfoMap.get(buildingId);
    }

    public BuildingConfig getBuildingConfig(FCBuilding building) {
        return buildingInfoMap.get(building.getBuildingId());
    }

    /**
     * 建设建筑，确保最小点在minLocation，旋转rotation
     * 
     * @param buildingId
     * @param minLocation
     * @param rotation
     * @return
     */
    public FCBuilding build(String buildingId, Location minLocation, int rotation) {
        // 判断该建筑是否有投影文件
        if (!BuildingUnitAPI.isSchematicExist(buildingId)) {
            FrostCraftBU.i().getLogger().info(FrostCraftBU.i().getConfig().getString("NoSchema"));
            return null;
        }
        // 不知道原点位置，首先随便假设建筑原点在minLocation
        var beforeOrigin = minLocation.clone();
        // 由于建筑绕原点旋转，需要计算旋转后的最小点距离目标最小点的偏移，然后把原点移动过去。
        var beforePasteReigon = BuildingUnitAPI.getPasteRegion(beforeOrigin, buildingId, rotation);
        var offset = minLocation.clone().subtract(beforePasteReigon.minLocation);
        var afterOrigin = beforeOrigin.clone().add(offset);
        var afterPasteReigon = BuildingUnitAPI.getPasteRegion(afterOrigin, buildingId, rotation);
        // 判断建筑空间是否足够
        // var buildingSize = getBuildingConfig(buildingId).getSize();
        var overlapUnits = BuildingUnitAPI.getOverlapUnits(afterPasteReigon.minLocation, afterPasteReigon.maxLocation);
        if (overlapUnits.size() > 0) {
            FrostCraftBU.i().getLogger().info("建筑" + buildingId + "在" + afterOrigin.toString() + "粘贴失败，因为空间不足");
            return null;
        }
        // 粘贴建筑
        var unitInfo = BuildingUnitAPI.pasteUnit(afterOrigin,
                buildingId, rotation, true);
        try {
            var fcbuilding = initBuilding(buildingId, unitInfo.getUuid(), true);
            addBuilding(fcbuilding);
            return fcbuilding;
        } catch (Exception exception) {
            exception.printStackTrace();
            BuildingUnitAPI.deleteUnit(unitInfo, true);
            return null;
        }
    }

    public boolean removeBuilding(FCBuilding building, boolean toEmptyLand) {
        // 0. 先行获取必要值
        var center = building.getCenter();
        var minLoc = building.getMinLocation();
        // 获取建筑的LandType
        var consSiteType = building.getBuildingConfig().getConsSiteType();

        var landTypeMap = FrostCraftBU.i().getConfig().getConfigurationSection("EmptyLandSign2List");
        if (consSiteType == null) {
            if (landTypeMap.getValues(false).values().contains(building.getBuildingId())) {
                consSiteType = building.getBuildingId();
            } else {
                consSiteType = "小工地";
            }
        }
        final String finalConsSiteType = consSiteType;
        var landTypeName = landTypeMap
                .getValues(false).keySet().stream().filter(s -> landTypeMap.getString(s).equals(finalConsSiteType))
                .findFirst().orElseGet(() -> "小地皮"); // 反向获取key值，获取landType对应的key值，即地皮类型名称。
        // 1. 调用所有模块的 onUnload
        if (building.getModules() != null) {
            for (BaseModule module : building.getModules().values()) {
                try {
                    module.onUnload();
                } catch (Exception e) {
                    FrostCraftBU.i().getLogger().severe("Error unloading module " + module.getModuleTypeId()
                            + " for building " + building.getUnitUUID() + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }

        // 3. 从 buildingMap 移除
        buildingMap.remove(building.getUnitUUID());

        // 4. 删除实际的建筑单元
        BuildingUnitAPI.deleteUnit(BuildingUnitAPI.getUnit(building.getUnitUUID()), true);
        FrostCraftBU.i().getLogger().info("Removed building: " + building.getUnitUUID());
        // 5. 设置空地牌子
        if (toEmptyLand) {

            var signLoc = center;
            signLoc.setY(minLoc.getY());
            // 将该位置设为木牌
            signLoc.getBlock().setType(Material.OAK_SIGN);
            if (signLoc.getBlock().getState() instanceof Sign) {
                var signState = (Sign) signLoc.getBlock().getState();
                signState.getSide(Side.FRONT).setLine(0, FrostCraftBU.i().getConfig().getString("EmptyLandSign0"));
                signState.getSide(Side.FRONT).setLine(1, FrostCraftBU.i().getConfig().getString("EmptyLandSign1"));
                signState.getSide(Side.FRONT).setLine(2, (String) landTypeName);
                signState.getSide(Side.FRONT).setLine(3, "");
                var signData = (org.bukkit.block.data.type.Sign) signState.getBlockData();
                signData.setRotation(BlockFace.NORTH_WEST);
                signState.setBlockData(signData);
                signState.update();
            }
        }
        return true;
    }

    public boolean removeBuildingAt(Location loc, boolean toEmptyLand) {
        // 判断建筑是否存在
        var unit = BuildingUnitAPI.getUnit(loc);
        FrostCraftBU.i().getLogger().info("removeBuildingAt:" + loc.toString());
        if (unit == null)
            return false;
        FrostCraftBU.i().getLogger().info("find unit:" + unit.toString());
        var unitUUID = unit.getUuid();

        var building = getBuilding(unitUUID);
        if (building == null)
            return false;
        FrostCraftBU.i().getLogger().info("find building:" + building.toString());
        // 删除建筑
        removeBuilding(building, toEmptyLand);
        return true;
    }

    public void addBuilding(FCBuilding building) {
        buildingMap.put(building.getUnitUUID(), building);
    }

    /**
     * 找到uuid对应的建筑
     * 
     * @param buildingUUID
     * @return
     */
    public FCBuilding getBuilding(String buildingUUID) {
        return buildingMap.get(buildingUUID);
    }

    public FCBuilding getBuildingAt(Location loc) {
        var unitAtLoc = BuildingUnitAPI.getUnit(loc);
        if (unitAtLoc == null)
            return null;
        var buildingUUID = unitAtLoc.getUuid();
        if (getBuilding(buildingUUID) != null)
            return getBuilding(buildingUUID);
        return null;
    }

    public Collection<FCBuilding> getAllBuildings() {
        return buildingMap.values();
    }
}
